apiVersion: apps/v1
kind: Deployment
metadata:
  name: jtkl-frontend-console
  namespace: ${JT_MGM_NAMESPACE}
spec:
  replicas: ${JTKL_FRONTEND_POD_REPLICAS}
  selector:
    matchLabels:
      app: jtkl-frontend-console
  template:
    metadata:
      labels:
        app: jtkl-frontend-console
        microservice-topology-group: jtkl
    spec:
      topologySpreadConstraints:
        - maxSkew: ${MICROSERVICE_TOPOLOGY_MAX_SKEW}
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
          labelSelector:
            matchLabels:
              microservice-topology-group: jtkl
      imagePullSecrets:
        - name: registry.cmri.cn.key
      terminationGracePeriodSeconds: 10
      containers:
        - name: jtkl-frontend-console
          image: ${HARBOR_BASE_URL}/${JT_SVC_IMAGE_PREFIX}/jtkl-frontend-console:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
              protocol: TCP
          resources:
            requests:
              ${JTKL_FRONTEND_POD_REQUESTS_CPU}
              ${JTKL_FRONTEND_POD_REQUESTS_MEMORY}
            limits:
              ${JTKL_FRONTEND_POD_LIMIT_CPU}
              ${JTKL_FRONTEND_POD_LIMIT_MEMORY}
          securityContext:
            runAsUser: ${CONTAINER_USER_ID}
          env:
            # 配置中心url
            - name: CONFIG_CENTER_URL
              valueFrom:
                configMapKeyRef:
                  name: jtkl-settings-env-configure
                  key: config-center.url

