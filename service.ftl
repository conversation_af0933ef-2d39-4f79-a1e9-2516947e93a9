<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" href="/notice/web/images/favicon.jpg">
  <title>焕新社区</title>
  <style>
    * {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol' !important;
      box-sizing: border-box;
    }

    html,
    body,
    #app {
      height: 100%;

    }

    body {
      font-size: 14px;
      font-weight: 400;
    }

    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    ::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 6px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background-color: rgba(0, 0, 0, 0.4);
    }

    ::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
      border-radius: 6px;
    }

    a {
      color: inherit;
      text-decoration: none;

    }

    a:hover {
      color: inherit;
    }

    a[disabled] {
      cursor: not-allowed;
      pointer-events: all;
    }

    ul,
    ol {
      list-style: none;
    }

    p,
    ul,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-bottom: 0;
    }
  </style>

  <style>
    header{
      height: 80px;
      background: #FFFFFF;
      border: 1px solid #EDF1F3;
      padding: 0 32px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    header img{
      height: 35px;
    }
    header .button{
      width: 106px;
      height: 40px;
      border-radius: 2px;
      border: 1px solid #178CF9;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #178CF9;
      line-height: 40px;
      text-align: center;
      cursor: pointer;
      user-select: none;
    }
    header .button:hover{
      background: #F0F7FF;
    }
    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 211px;
      flex-wrap: wrap;
      position: relative;
    }

    .content img {
      width: 416px;
    }

    .img-text {
      position: absolute;
      top: 300px;
    }

    p {
      width: 100%;
      text-align: center;
    }

    p:nth-of-type(1) {
      font-size: 28px;
      line-height: 34px;
      font-weight: 600;
    }

    p:nth-of-type(2) {
      margin-top: 16px;
      font-size: 18px;
      color: #606972;
      line-height: 30px;
    }

    p:nth-of-type(2) span {
      color: #178cf9;
    }
  </style>
</head>

<body keycloakurl="keycloakurl" path="path">
  <header>
    <img src="/notice/web/images/header-logo.png" alt="">
    <div class="button" id="logout">退出登录</div>
  </header>
<div class="content">
  <img src="/notice/web/images/系统维护@2x.png" alt="" />
  <div class="img-text">
    <p>${noticeMessage.title}</p>
    <p>
       ${noticeMessage.content}</p>
  </div>
</div>
</body>

<script src="http://libs.baidu.com/jquery/2.0.0/jquery.min.js"></script>
<script>
  document.querySelector("#logout").onclick = (e)=>{
    const path = document.querySelector('body').getAttribute('path');
    const keycloakUrl = document.querySelector('body').getAttribute('keycloakurl');
    console.log(path,keycloakUrl)
    $.ajax({
        url: '/course_model/web/logout', //url地址
        contentType: "application/json",
        dataType: "json", //返回的数据类型
        type: "get", //发起请求的方式
        success: function(data) {
            console.log(data);
            window.location.replace(`${keycloakUrl}/realms/TechnicalMiddlePlatform/protocol/openid-connect/logout?redirect_uri=${path}`);
        },
    });
  }
  setInterval(() => {
    $.ajax({
        url: '/notice/web/getNoticeStatus', //url地址
        contentType: "application/json",
        dataType: "json", //返回的数据类型
        type: "get", //发起请求的方式
        success: function(res) {
            console.log(res);
          if(res.state !== 'OK'){
            window.location.replace(`/web/#/home`);
          }
        },
    });
  }, 60000);
</script>
</html>