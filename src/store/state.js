const state = {
  userInfo: {},
  sideMenuAuth: [], // 侧边栏数据递归权限
  sideMenu: [], // 侧边栏数据
  layoutConfig: {
    iconTop: '',
    iconTopFold: '',
    link: '',
    subTab: '焕新社区',
    subOverviewTab: '焕新社区运管平台',
  },
  // 新建服务-服务部署方式
  instanceType: undefined,
  projectList: [], // 全量的项目空间列表
  projectId: null, //项目id
  projectInfo: {}, //项目信息
  poolList: [], // 资源池列表
  poolPolicy: {}, //资源池配置
  poolListLoading: true, // 初始化资源池列表加载状态
  projectPolicy: {}, //项目相关配置
  poolInfo: {
    // 当前选中的资源
    id: null,
    name: '',
  }, // 资源池信息
  projectRole: [], // 当前用户的项目角色
  menuHasProject: false, // 菜单是否具有项目选择
  menuHasPlatform: false, // 菜单是否具有运管平台标识
  tokenAuth: false, // 推理服务-模型服务是否开启安全鉴权
  globalLoading: false, // 全局loading
  globalLoadingTitle: '', // 全局loading文案
  globalLoadingText: '', // 全局loading文案
  menuDataLoaded: false, // 菜单数据是否加载完成
  startCommand: '', // 推理服务-模型部署-平台预置镜像的时候，需要回填所选择的镜像的启动命令
  jtFeedbackModalVisible: false, // 问题反馈弹窗
  mirrorUploadingList: null, // 镜像文件正在导入列表数据备份
  modelUploadingList: null, // 模型文件正在导入列表数据备份
  token: '', // kc token，现阶段用于推理服务详情页在线调试headers展示
  workDir: '', // 推理服务-模型部署-汇聚模型部署需要回填的启动命令
  systemMaintenanceStatus: { data: null, code: 1 }, // 系统维护状态
  gpuCardMax: { data: 0 }, // 获取开发环境最大限制卡数
  routeViewRefreshKey: new Date().toString(),
};
export default state;
