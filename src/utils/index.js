import store from '@/store';
import { project<PERSON>pace<PERSON><PERSON> } from '@/apis';
import { message } from 'ant-design-vue';
import moment from 'moment';
import { setItem } from '@/utils/storageUtil';
import { PROJECT_KEY, POOLID_KEY } from '@/constants/index';
import { portalPrefixUrl } from '@/config';

/**
 * @description 打开新的窗口(规避直接open跳转产生的代码审计风险问题)
 * @param {String} url 跳转路径
 */
export function openInNewTab(url) {
  const newWin = window.open(url) || {};
  newWin.opener = null;
}

/**
 * @description 判断跳转路径的类型
 * @param {String} url 地址参数
 * @returns {Boolean} 是否是绝对路径
 */
export function isAbsolutePath(url) {
  if (!url) return;
  return RegExp(/(http|https):\/\/www\.[a-zA-Z0-9]+\.[a-zA-Z0-9]+|www\.[a-zA-Z0-9]+\.[a-zA-Z0-9]+|[a-zA-Z0-9]+\.[a-z0-9]+/).test(url);
}

/**
 * @description 给url拼接query参数
 * @param {String} url - 路径
 * @param {Object} urlParams - 参数对象
 * @returns {String} 返回拼接后的路径
 */
export function addUrlParams(url, urlParams) {
  let baseURL = new URL(url);
  let params = new URLSearchParams(urlParams);
  baseURL.search = params.toString();
  return baseURL.href;
}

/**
 * @description 去掉字符串最后一个斜杠
 * @param {String} str
 * @returns
 */
export function removeTrailingSlash(str) {
  if (str.charAt(str.length - 1) === '/') {
    return str.slice(0, -1);
  }
  return str;
}

/**
 * @description 数据异常时的显示状态（常用于table）
 * @param {String | Number} value 输入值
 * @param {*} noTextData 空态需要返回的内容
 * @returns {*} 返回的显示值
 */
export function checkTextEmpty(value, noTextData = '--') {
  if (value === null || value === undefined || value === '') {
    return noTextData;
  } else {
    return value;
  }
}

/**
 * @description 浏览器复制内容到粘贴板
 * @param {String} text - 复制的内容
 * @param {String} msg - 复制成功提示文案
 * @returns {Boolean} 是否复制成功
 */
export function handleCopy(text, msg = '') {
  try {
    var oInput = document.createElement('input');
    oInput.style.border = '0 none';
    oInput.style.color = 'transparent';
    oInput.value = text;
    document.body.appendChild(oInput);
    oInput.select();
    document.execCommand('Copy');
    oInput.parentNode.removeChild(oInput);
    message.success(msg || '复制成功');
    return true;
  } catch (err) {
    return false;
  }
}

/**
 * @description 浏览器复制内容到粘贴板(支持保留文本格式)
 * @param {String} text - 复制的内容
 * @param {String} msg - 复制成功提示文案
 * @returns {Boolean} 是否复制成功
 */
export function handleCopyWithFormat(text, msg = '') {
  try {
    var oInput = document.createElement('textarea');
    oInput.style.border = '0 none';
    oInput.style.color = 'transparent';
    oInput.value = text;
    document.body.appendChild(oInput);
    oInput.select();
    document.execCommand('Copy');
    oInput.parentNode.removeChild(oInput);
    message.success(msg || '复制成功');
    return true;
  } catch (err) {
    return false;
  }
}

/**
 * @description 手机号隐藏中间四位
 * @param {String} phoneNumber 电话号码
 * @returns {String} 替换后的电话号码
 */
export function hidePhone(phoneNumber) {
  const innerPhone = phoneNumber || '';
  return innerPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

/**
 * @description 数字转成汉字
 * @param {Number} num 要转换的数字
 * @return {String} 转换后的汉字
 * */
export function toChinesNum(num) {
  var arr1 = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  var arr2 = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿']; //可继续追加更高位转换值
  if (!num || isNaN(num)) {
    return '零';
  }
  var english = num.toString().split('');
  var result = '';
  for (var i = 0; i < english.length; i++) {
    var des_i = english.length - 1 - i; //倒序排列设值
    result = arr2[i] + result;
    var arr1_index = english[des_i];
    result = arr1[arr1_index] + result;
  }
  result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十');
  result = result.replace(/零+/g, '零');
  result = result.replace(/零亿/g, '亿').replace(/零万/g, '万');
  result = result.replace(/亿万/g, '亿');
  result = result.replace(/零+$/, '');
  result = result.replace(/^一十/g, '十');
  return result;
}

/**
 * @description 修改url锚点前的query参数，可传入参数值为空清空该query参数，如{poolId: '', projectId: ''}
 * @param {String} url - 路径
 * @param {Object} params - 更新的参数对象
 * @returns {String} 返回拼接后的路径
 */
export function changeQueryBeforeHash(url = window.location.href, params = {}) {
  const [beforeHash, hash] = url.split('#', 2);
  const [domainAndPath, queryString] = beforeHash.split('?', 2);
  const queryParams = queryString ? new URLSearchParams(queryString) : new URLSearchParams();
  for (const key in params) {
    if (params.hasOwnProperty(key)) {
      const value = params[key];
      if (value || value === 0) {
        queryParams.set(key, params[key]);
      } else {
        queryParams.delete(key);
      }
    }
  }
  const updatedQueryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
  return `${domainAndPath}${updatedQueryString}${hash ? `#${hash}` : ''}`;
}

/**
 *
 * @param {string} url
 * @returns {Object} 返回参数对象
 */
export function getQueryObjBeforeHash(url = window.location.href) {
  const [beforeHash] = url.split('#', 2);
  const [domainAndPath, queryString] = beforeHash.split('?', 2);
  return queryStringToObject(queryString);
}

/**
 * 对指定URL上指定参数进行移除操作
 * @param {*} url 指定url
 * @param {*} alias 指定的参数alias标识
 * @param {*} order 是否在#前后的参数，默值-0；0-全部，1-#之前的，2-#之后的
 */
export function removeUrlParamBykey(url, alias = '', order = 0) {
  if (!url) return url;
  let ishasKey = false;
  const currentUrl = new URL(url);
  const searchParams = currentUrl.searchParams;
  if ([0, 1].includes(order) && searchParams?.has(alias)) {
    searchParams?.delete(alias);
    ishasKey = true;
  }
  if ([0, 2].includes(order) && currentUrl.hash) {
    let beforeUrl = new URL(`${window.location.origin}${currentUrl.hash.substring(1)}`);
    const beforeSearchParams = beforeUrl.searchParams;
    if (beforeSearchParams?.has(alias)) {
      beforeSearchParams?.delete(alias);
      beforeUrl = '#' + beforeUrl.toString().replace(window.location.origin, '');
      currentUrl.hash = beforeUrl;
      ishasKey = true;
    }
  }
  const resultUrl = currentUrl.toString();
  if (ishasKey) {
    replaceCurrentUrl(resultUrl);
  }
  return resultUrl;
}

/**
 * 对指定URL上指定参数进行修改操作
 * @param {*} url 指定url
 * @param {*} alias 指定的参数alias
 * @param {*} value 指定的参数的value
 * @param {*} order 是否在#前后的参数，默值-1；1-#之前的，2-#之后的
 */
export function changeUrlParamsByKey(url, alias = '', value = '', order = 1) {
  if (!url || !alias) return url;
  const currentUrl = new URL(url);
  const searchParams = currentUrl.searchParams;
  if (order === 1) {
    searchParams?.set(alias, value);
  }
  if (order === 2 && currentUrl.hash) {
    let beforeUrl = new URL(`${window.location.origin}${currentUrl.hash.substring(1)}`);
    const beforeSearchParams = beforeUrl.searchParams;
    beforeSearchParams?.set(alias, value);
    beforeUrl = '#' + beforeUrl.toString().replace(window.location.origin, '');
    currentUrl.hash = beforeUrl;
  }
  const resultUrl = currentUrl.toString();
  return resultUrl;
}

/**
 *
 * @param {String} queryString 拼接的字符串
 * @returns {Object} 返回参数对象
 */
export function queryStringToObject(queryString) {
  if (!queryString) return {};
  const pairs = queryString.split('&');
  const result = {};

  for (let i = 0; i < pairs.length; i++) {
    const pair = pairs[i];
    const [key, value] = pair.split('=');
    result[decodeURIComponent(key)] = decodeURIComponent(value || '');
  }

  return result;
}

/**
 * @description 更新当前浏览器地址，页面不刷新
 * @param {String} url 更新的url
 */
export function replaceCurrentUrl(updateUrl) {
  window.history.replaceState({}, '', updateUrl);
}

/**
 * @description 获取地址栏参数
 * @param
 */
export function getQueryStringParameters() {
  const queryString = window.location.search;
  const hasHash = window.location.href.includes('#');
  const hash = hasHash ? window.location.hash.substring(1) : '';

  // 排除锚点之后的部分
  const queryParamsString = hasHash ? queryString.split('#')[0] : queryString;

  const params = new URLSearchParams(queryParamsString);

  const queryParams = {};
  for (const [key, value] of params.entries()) {
    queryParams[key] = value;
  }

  return { queryParams, hash };
}

/**
 * @description 更新地址栏id&刷新页面；若传参有projectId，同时调用接口更新项目访问记录
 * @param {Object} urlParams - 需要更新的资源池id和项目id
 * @param {Boolean} needRefresh - 是否需要刷新页面，默认需要刷新
 * @param {String} url - 需要重置的url地址
 */
export function updateUrlAndRefresh(urlParams = {}, needRefresh = true, url = window.location.href) {
  if (urlParams?.projectId) {
    projectSpaceApi.visitProject();
  }
  store.commit('UPDATE_PROJECT_ID', urlParams?.projectId);
  if (urlParams?.projectId !== undefined) {
    setItem(PROJECT_KEY, urlParams?.projectId);
  }
  if (urlParams?.poolId !== undefined) {
    setItem(POOLID_KEY, urlParams?.poolId);
  }
  replaceCurrentUrl(changeQueryBeforeHash(url, urlParams));
  needRefresh && store.commit('UPDATE_ROUTEVIEWREFRESHKEY');
}
// 赋予一个通用的getKey方法，可以通过属性值反查属性名
export const addGetKeyMethod = (obj) => {
  obj.getKey = (value) => {
    return Object.keys(obj).find((key) => obj[key] === value);
  };
};

// obj 转 key value 数组
export const transformObjectToArray = (obj, keyMapping = { value: 'value', text: 'text' }) => {
  return Object.entries(obj).map(([value, text]) => ({
    [keyMapping.value]: value,
    [keyMapping.text]: text,
  }));
};

/**
 * @description 向url后面追加query参数
 * @param {string} url
 * @param {object} params
 * @returns 拼接后的url
 */
export function appendQueryParamsToUrl(url, params) {
  let query = Object.keys(params)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');

  if (url.indexOf('?') === -1) {
    url += '?' + query;
  } else {
    url += '&' + query;
  }

  return url;
}

/**
 * @description 根据当前的域名返回WebSocket的URL前缀
 * @returns websocket域名前缀
 */
export function getWebSocketProtocolAndDomain() {
  const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
  const hostnameWithPort = window.location.hostname + (window.location.port ? ':' + window.location.port : '');
  return `${protocol}${hostnameWithPort}`;
}

// 获取耗时格式化成 xx:xx:xx
export const getFormatDuringTime = (time) => {
  let result = '';
  const h = parseInt(time / 3600) + '';
  const m = parseInt((time - h * 3600) / 60) + '';
  const s = parseInt(time - h * 3600 - m * 60) + '';
  result = `${h.padStart(2, '0')}:${m.padStart(2, '0')}:${s.padStart(2, '0')}`;
  return result;
};

// 获取耗时格式化成 X天X小时X分X秒
export const formatTime = (time) => {
  if (time > 24 * 3600) {
    const day = parseInt(time / (24 * 3600));
    const h = parseInt((time - day * 24 * 3600) / 3600);
    const m = parseInt((time - day * 24 * 3600 - h * 3600) / 60);
    const s = time - day * 24 * 3600 - h * 3600 - m * 60;
    return `${day}天${h}小时${m}分${s}秒`;
  } else if (time > 3600) {
    const h = parseInt(time / 3600);
    const m = parseInt((time - h * 3600) / 60);
    const s = time - h * 3600 - m * 60;
    return `${h}小时${m}分${s}秒`;
  } else if (time > 60) {
    const m = parseInt(time / 60);
    const s = time - m * 60;
    return `${m}分${s}秒`;
  } else {
    return `${time}秒`;
  }
};

// 获取时间格式化
export const getFormatTime = (time, formatStr = 'YYYY-MM-DD HH:mm:ss') => {
  if (time) {
    return moment(time).format(formatStr);
  } else {
    return '--';
  }
};

// 转化成GB等其他单位
export const toGB = (sizeNum, fixedNum = 2, { unitDisplayFull = false, displayFullField = false, unitIfZero = true } = {}) => {
  let size = '';
  let unit = '';
  const limit = parseInt(sizeNum ? sizeNum : 0);
  let obj = {
    size,
    unit,
  };
  if (limit === 0) {
    size = limit.toFixed(fixedNum);
    unit = unitIfZero ? 'G' : 'B';
  } else if (limit < 1024) {
    size = limit.toFixed(fixedNum);
    unit = 'B';
  } else if (limit < 1024 * 1024) {
    size = (limit / 1024).toFixed(fixedNum);
    unit = unitDisplayFull ? 'KB' : 'K';
  } else if (limit < 1024 * 1024 * 1024) {
    size = (limit / (1024 * 1024)).toFixed(fixedNum);
    unit = unitDisplayFull ? 'MB' : 'M';
  } else if (limit < 1024 * 1024 * 1024 * 1024) {
    size = (limit / (1024 * 1024 * 1024)).toFixed(fixedNum);
    unit = unitDisplayFull ? 'GB' : 'G';
  } else if (limit < 1024 * 1024 * 1024 * 1024 * 1024) {
    size = (limit / (1024 * 1024 * 1024 * 1024)).toFixed(fixedNum);
    unit = unitDisplayFull ? 'TB' : 'T';
  } else {
    size = (limit / (1024 * 1024 * 1024 * 1024 * 1024)).toFixed(fixedNum);
    unit = unitDisplayFull ? 'PB' : 'P';
  }
  obj = {
    size,
    unit,
  };
  if (displayFullField) {
    return obj.size + obj.unit;
  }
  return obj;
};

const generateRandomString = (length) => {
  const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

// 生成一个随机的字符串
export const stringUnix = (projectId) => {
  return `${projectId}-${generateRandomString(5)}-${Date.now()}`;
};

// 打开新的标签页并跳转到指定页面
export const gotoNewTarget = (url) => {
  const a = document.createElement('a');
  const event = new MouseEvent('click');

  a.href = url.href;
  a.target = '_blank';
  a.rel = 'noopener noreferrer';
  a.dispatchEvent(event);
};

// 校验中文与中文标点符号正则
export const checkUnicodeText = (text) => {
  const reg = /[(\u4e00-\u9fa5)(\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3010|\u3011|\u007e)]+/g;
  return reg.test(text);
};

// location跳转模式的路由跳转(跳转会刷新页面), 用于替代router.push
export const locationRouter = {
  push: ({ path, query }) => {
    const [locationPath, hash] = location.href.split('#');
    const targetUrl = `${locationPath}#${path}?${new URLSearchParams(query).toString()}`;
    window.history.pushState({}, '', targetUrl);
    location.reload();
  },
};

// 切换资源池或退出资源池，刷新到指定的页面(默认到概览页)
export const changePoolAndReload = (poolId = null, routeUrl = '#/home') => {
  console.log('🚀 ~ changePoolAndReload ~ poolId:', poolId);
  const homeUrl = window.location.href.substring(0, window.location.href.indexOf('#')) + routeUrl;

  updateUrlAndRefresh({ poolId, projectId: null }, false, homeUrl);
  window.location.reload();
};

// 将url中的query参数转为对象
export const urlParamsToObject = (url) => {
  const urlParams = new URL(url).searchParams;
  const paramsObject = {};
  for (const [key, value] of urlParams) {
    paramsObject[key] = value;
  }
  return paramsObject;
};
/**
 *
 * @returns 跳转到门户相关平台的域名和前缀（用于区分不同环境）
 * @description 用于平台的跳转，如帮助中心、消息中心等，测试环境比较特殊使用，域名不同，因此需在VUE_APP_PORTAL_ROUTE_PREFIX单独配置
 */
export const getRedirectUrlPrefix = () => {
  return `${process.env.VUE_APP_PORTAL_ROUTE_PREFIX || window.location.origin}${portalPrefixUrl}/`;
};

export const isValidTimestamp = (timestamp) => {
  const parsedTimestamp = moment(timestamp);
  return parsedTimestamp.isValid();
};

// 入口资源是否为国资
export const isEntranceBySasac = () => {
  const entrance = getQueryObjBeforeHash()?.entrance || ''; // 系统来源（默认），sasac-国资
  return entrance === 'sasac';
};

/**
 *
 * @param {string} type - mgt / apv 后端定义的管理或审批权限
 * @param {object} authObj - kc返回的权限对象
 * @param {*} parentKey - 如果有多层的对象包裹形式，需要进行key值的拼接
 * @returns 权限数组
 */
export const flattenKcauthList = (type, authObj, parentKey = '') => {
  let result = [];
  if (Array.isArray(authObj)) {
    return authObj.map((value) => {
      return {
        type: type,
        key: parentKey,
        value: value,
      };
    });
  }
  Object.keys(authObj).forEach((key) => {
    if (Array.isArray(authObj[key])) {
      authObj[key].forEach((auth) => {
        result.push({
          type: type,
          key: parentKey ? `${parentKey}-${key}` : key,
          value: auth,
        });
      });
    } else if (typeof authObj[key] === 'object') {
      result = [...result, ...flattenKcauthList(type, authObj[key], parentKey ? `${parentKey}-${key}` : key)];
    } else {
      result.push({
        type: type,
        key: parentKey ? `${parentKey}-${key}` : key,
        value: authObj[key],
      });
    }
  });
  return result;
};
/**
 * @description arr数组按照集合collection进行重新排序
 * @param {*} arr
 * @param {*} collection
 * @returns 排序后的数组
 */
export const sortArrByCollection = (arr, collection) => {
  const res = [];
  collection.forEach((key) => {
    if (arr.includes(key)) {
      res.push(key);
    }
  });
  return res;
};

// 通用的科学计数法转数字
export const scientificToNumber = (inputNumber) => {
  if (isNaN(inputNumber)) {
    return inputNumber;
  }
  inputNumber = '' + inputNumber;
  inputNumber = parseFloat(inputNumber);
  let eformat = inputNumber.toExponential(); // 转换为标准的科学计数法形式（字符串）
  let tmpArray = eformat.match(/\d(?:\.(\d*))?e([+-]\d+)/); // 分离出小数值和指数值
  let number = inputNumber.toFixed(Math.max(0, (tmpArray[1] || '').length - tmpArray[2]));
  return number;
};

export function jsonParse(data) {
  try {
    return JSON.parse(data);
  } catch (error) {
    if (data.includes('[')) {
      return [];
    }
    if (data.includes('{')) {
      return {};
    }
    return data;
  }
}
// 用于设置a-table的filter的挂载父元素，保证滚动时显示正常
export const getPopupContainer = () => document.getElementsByClassName('ant-layout-content')?.[0] || document.body;
