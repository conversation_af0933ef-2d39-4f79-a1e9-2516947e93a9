/* less全局变量 */

// 颜色类
@jt-primary-color: #00A0CC;
@jt-link-button-hover-color: #2bb4d6;
@jt-color-white: #fff;
@jt-text-color-disabled: rgba(0,20,26,0.25);;
@jt-text-color-primary: rgba(0, 20, 26, 1);
@jt-text-color-primary-opacity07: rgba(0, 20, 26, 0.7);
@jt-text-color-primary-opacity05: rgba(0, 20, 26, 0.5);
@jt-text-color-primary-opacity008:rgba(0, 20, 26, 0.08);
@jt-text-color-primary-opacity045: rgba(0, 20, 26, 0.45);
@jt-text-color: #606972; // 灰色文字颜色
@jt-text-color-secondary: #A0A6AB; // 二级灰色文字颜色
@jt-title-color: @jt-text-color-primary; // 标题文字颜色：tab标题、弹框标题
@jt-background-color: #F1F9FF; // 背景底色
@jt-input-border-hover-color:#23b7d9; // input标签hover时的边框颜色
@jt-input-border-error-color:#fe3a47; // input标签error时的边框颜色
@jt-input-border-hover-error-color:#ff8c8c; // input标签error时hover的边框颜色

@jt-border-color: rgba(0, 20, 26, 0.15); // 边框颜色
@jt-disable-color: #CBCFD2; // 按钮disable颜色
@jt-line-color:#E0E1E1; // 分割线颜色
@jt-table-header-color: #333333; // table表头文字颜色、alert提示的文字颜色
@jt-color-background-layout: rgba(0, 20, 26, 0.04); // 未选中项hover时的背景颜色

@jt-error-color: #FF454D;
@jt-warn-color: #F79032;

// 字体大小
@jt-font-size-sm: 12px;
@jt-font-size-base: 14px;
@jt-font-size-lg: @jt-font-size-base + 2px;
@jt-font-size-lger: @jt-font-size-lg + 2px;
@jt-font-weight: 400;
@jt-font-weight-medium: 600;

// 圆角
@jt-border-radius: 2px;

@jt-card-border-radius: 4px;

// 按钮高度
@jt-btn-height-base: 32px;
@jt-btn-height-lg: 40px;

// 间距
@jt-gap-base: 8px;

// 盒子阴影
@jt-box-shadow: 0px 2px 4px 0px rgba(0,20,26,0.04);;

