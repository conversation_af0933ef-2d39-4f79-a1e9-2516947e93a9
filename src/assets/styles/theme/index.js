// antd4.0版本以上，样式定义，参考：https://www.antdv.com/docs/vue/customize-theme-cn#seedtoken
const theme = {
  token: {
    colorPrimary: '#00A0CC',
    colorLink: '#00A0CC',
    colorText: 'rgba(0,20,26,0.7)',
    colorBgSpotlight: '#fff',
    colorSuccess: '#00CCAA',
    colorSuccessText: '#00CCAA',
    colorError: '#FE3A47',
    colorErrorText: '#FE3A47',
    colorWarning: '#FF8C19',
    colorWarningText: '#FF8C19',
    colorBorderSecondary: 'rgba(0,20,26,0.08)',
    borderRadiusSM: '2px',
  },
  components: {
    Table: {
      colorTextHeading: '#00141A',
      fontWeightStrong: '400', // 取消table头的加粗效果
    },
    Tabs: {
      fontSize: '16px',
      colorSplit: 'rgba(0,20,26,0.08)',
      borderRadius: '2px',
    },
    Radio: {
      borderRadius: '2px',
    },
    Button: {
      borderRadius: '2px',
    },
    Input: {
      borderRadius: '2px',
    },
    Pagination: {
      borderRadius: '2px',
    },
    Select: {
      borderRadius: '2px',
      borderRadiusLG: '2px',
    },
    Notification: {
      borderRadiusLG: '4px',
      colorTextHeading: '#00141A',
    },
    Message: {
      borderRadiusLG: '2px',
    },
    Dropdown: {
      borderRadiusLG: '2px',
    },
    Modal: {
      borderRadiusLG: '4px',
    }
  },

}

export default theme;