/* 第三方库样式覆盖 */
// 搜索框
.ant-input-affix-wrapper {
  .ant-input-prefix {
    color: #bec2c5;
    margin-right: 8px;
  }
}
.ant-menu-dark .ant-menu-submenu-open {
  color: #d9d9d9 !important;
}

/*浏览器默认样式覆盖*/
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
	// font-size: 100%;
	// font: inherit;
	// vertical-align: baseline;
}
/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section {
	display: block;
}
body {
  line-height: 1;
  background: @jt-background-color;
  overflow-y: hidden;
}
ol, ul {
	list-style: none;
}
blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
	content: '';
	content: none;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}

*{
    box-sizing: border-box;
}
// html {
//   overflow-y: hidden;
// }
::-webkit-scrollbar {
    width : 6px;
    height: 6px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(0, 20, 10, 0.15);
    border-radius   : 6px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 20, 10, 0.25);
}

::-webkit-scrollbar-track {
    box-shadow   : inset 0 0 6px rgba(0, 20, 10, 0.04);
    border-radius: 6px;
}

// iconfont图标支持颜色修改
svg {
    fill: currentColor;
  }
  
  path {
    fill: unset;
  } 