<template>
  <jt-micro-components :app-name="appName" :module-name="moduleName" :data="{ requester, config, titleStyle }" />
</template>

<script>
import { GET, POST } from '@/request/requestMicroComponents';
const MIRCOAPPNAME = 'unread-message';
import { getRedirectUrlPrefix } from '@/utils/index';
const urlPrefix = getRedirectUrlPrefix();

export default {
  name: 'UnreadMessageRemote',
  props: {
    showIcon: {
      type: Boolean,
      default: false,
    },
    text: {
      type: String,
    },
    titleStyle: {
      type: Object,
      default: () => {},
    },
    appName: {
      type: String,
      default: () => Math.random().toString(36).substring(2, 15),
    },
    reload: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      moduleName: MIRCOAPPNAME,
      requester: { GET, POST },
      config: {
        isLogin: true,
        messageUrl: `${urlPrefix}message`,
        showIcon: this.showIcon,
        text: this.text,
        reload: this.reload,
      },
    };
  },
};
</script>
