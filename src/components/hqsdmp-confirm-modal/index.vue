<template>
  <a-modal v-bind="$attrs" wrap-class-name="modal-confirm-style" @cancel="handleCancel">
    <template #title>
      <div class="modal-title">
        <slot name="icon"><ExclamationCircleFilled class="warning-icon" :style="{ color: iconColor }" /></slot>{{ title }}
      </div>
    </template>
    <template #footer>
      <a-space>
        <a-button v-if="footerType === 'delete'" type="primary" danger :loading="confirmLoading" :disabled="confirmDisabled" @click="handleOk">{{ okText }}</a-button>
        <a-button @click="handleCancel">{{ cancelText }}</a-button>
        <a-button v-if="footerType === 'confirm'" type="primary" class="kl-create-btn" :loading="confirmLoading" :disabled="confirmDisabled" @click="handleOk">{{ okText }}</a-button>
      </a-space>
    </template>
    <template v-for="(item, key, index) in $slots" :key="index" #[key]>
      <slot :name="key" />
    </template>
  </a-modal>
</template>

<script setup>
import { defineProps } from 'vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
defineProps({
  title: {
    type: [String, HTMLElement],
    default: '',
  },
  confirmDisabled: {
    type: Boolean,
    default: false,
  },
  cancelText: {
    type: String,
    default: '取消',
  },
  confirmLoading: {
    type: Boolean,
    default: false,
  },
  okText: {
    type: String,
    default: '删除',
  },
  iconColor: {
    type: String,
    default: '#f53922',
  },
  footerType: {
    type: String,
    default: 'delete', // delete or comfirm
  },
});

const emit = defineEmits(['cancel', 'ok']);
const handleOk = () => {
  emit('ok');
};
const handleCancel = () => {
  emit('cancel');
};
</script>
<style lang="less">
.modal-confirm-style {
  .ant-modal-body {
    margin-left: 0;
    margin-top: 20px;
  }
  .ant-modal-close {
    margin-top: 7px;
  }
  .ant-modal-content {
    padding-top: 24px;
  }
  .ant-modal-footer {
    margin-top: 24px;
  }
}
</style>
<style lang="less" scoped>
.modal-confirm-style {
  .modal-title {
    display: inline-flex;
    align-items: center;
  }

  .ant-btn-primary.ant-btn-dangerous:disabled {
    background: #fbb7a5 !important;
    color: #fff;
    border-color: #fbb7a5;
  }
}

.warning-icon {
  font-size: 21px;
  color: #f53922;
  margin-right: 8px;
}
</style>
