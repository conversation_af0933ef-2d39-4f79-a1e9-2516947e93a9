<template>
  <span :color="color" :class="colorClass" :rounded="rounded" :num="num">
    <span class="with-background">{{ text }}</span
    ><span class="num">{{ num }}</span>
  </span>
</template>
<script setup>
import { computed } from 'vue';

const colorClass = computed(() => {
  // return props.bordered ? [props.color, 'border-has'] : [props.color, 'border-none'];
  const rootStyle = 'root';
  const roundedStyle = `rounded-${props.rounded}`;
  return [props.color, rootStyle, roundedStyle];
});

const props = defineProps({
  color: {
    type: [String, null],
    default: () => 'default',
    validator(value) {
      const COLORS_ENUM = ['orange', 'blue', null];
      return COLORS_ENUM.includes(value);
    },
  },

  rounded: {
    type: [String],
    default: () => 'small',
    validator(value) {
      const ROUNDED_ENUM = ['large', 'small', null];
      return ROUNDED_ENUM.includes(value);
    },
  },
  text: {
    type: [String, null],
    default: '',
  },

  num: {
    type: [Number, null],
    default: 1,
  },
});
</script>

<style lang="less" scoped>
.root {
  border-width: 1px;
  border-style: solid;
  display: inline-flex;
  height: 24px;
}
.num {
  width: 24px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-weight: 400;
  font-size: 12px;
  color: rgba(0, 20, 26, 0.7);
  line-height: 18px;
}

.rounded-large {
  border-radius: 12px;
}
.rounded-small {
  border-radius: 2px;
}

.blue {
  background: none;
  border-color: #6bb0fb;
  color: #0289ff;
  .with-background {
    background: #e6f4ff;
    padding: 0 4px;
    font-weight: 400;
    font-size: 12px;
    line-height: 21px;
  }
}
.orange {
  background: none;
  border-color: #fbc075;
  color: #f87c00;
  .with-background {
    background: #fff7e6;
    padding: 0 4px;
    font-weight: 400;
    font-size: 12px;
    line-height: 21px;
  }
}
</style>
