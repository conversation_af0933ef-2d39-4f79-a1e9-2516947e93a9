/**
 * 根据时间分类获取需要显示的对应的时间
 * @param type 按分钟、按小时等时间分类
 * @returns 对应的时间
 */
export function getTableTime() {
  const _dayTime = new Date(new Date().toLocaleDateString()).getTime(); // 当天0点的时间毫秒数
  const time = {
    beginTime: _dayTime - 1000 * 60 * 60 * 24 * 365,
    endTime: _dayTime,
  };
  return time;
}

/**
 * 根据后台返回字段获取 运行实例 文案
 * @param instanceName 字段名称
 */
export function getInstanceNameText(instanceName) {
  const storageName = {
    competition: '比赛',
    course: `学习`,
    job: `求职`,
    model: `建模`,
  };
  return storageName[instanceName];
}
/**
 * 根据后台返回字段获取 算力累计时长 文案
 * @param cumulativeDuration 字段名称
 */
export function getCumulativeDurationText(cumulativeDuration) {
  const storageName = {
    consumedCount: '算力豆实际使用量',
    cpuDuration: `CPU实例时长`,
    gpuDuration: `GPU实例时长`,
    vgpuDuration: `vGPU实例时长`,
  };
  return storageName[cumulativeDuration];
}

/**
 * 日期格式化
 * @param fmt 日期格式(YYYY-mm-dd HH:MM)
 * @param date 时间戳
 * @returns 格式化后的日期
 */
export function dateFormat(fmt, date) {
  let ret;
  const opt = {
    'Y+': date.getFullYear().toString(), // 年
    'm+': (date.getMonth() + 1).toString(), // 月
    'd+': date.getDate().toString(), // 日
    'H+': date.getHours().toString(), // 时
    'M+': date.getMinutes().toString(), // 分
    'S+': date.getSeconds().toString(), // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };
  for (const k in opt) {
    ret = new RegExp('(' + k + ')').exec(fmt);
    if (ret) {
      fmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'));
    }
  }
  return fmt;
}

/**
 * 通过毫秒数获取后端接口所需时间格式
 * @param _date 时间戳（毫秒）
 * @param dimension 时间分类
 * @returns 后端所需时间格式
 */
export const getFormatString = (_date) => {
  const date = new Date(_date);
  const y = date.getFullYear();
  const m = date.getMonth() + 1;
  const d = date.getDate();
  const H = date.getHours();
  const M = date.getMinutes();
  // 2021-8-2T16:00:00Z
  return `${y}-${m}-${d}T${H}:${M}:00Z`;
};

/**
 * 通过时间分类获取当前图表默认显示的缩放比例
 * @param dimension 时间分类
 * @returns 缩放比例
 */
export const getTableDataZoomStart = (dimension) => {
  switch (dimension) {
    case 'minute':
      return (100 / 24) * 23;
    case 'hour':
      return (100 / 30) * 29;
    case 'day':
      return (100 / 365) * 351;
    case 'week':
      return (100 / 52) * 41;
    case 'month':
      return (100 / 12) * 11;
    default:
      return (100 / 24) * 23;
  }
};

/**
 * 根据数据获取Y轴刻度最大值
 * @param maxValue 最大值
 * @returns Y轴刻度最大值
 */
export function yAxisMax(maxValue) {
  if (isNaN(maxValue / 1) || maxValue / 1 < 10) {
    return 10;
  }
  const max = Math.ceil(maxValue);
  const itemValue = Math.ceil(max / 5);
  const mins = Math.ceil(itemValue / Math.pow(10, (itemValue + '').length - 1));
  const item = mins * Math.pow(10, (itemValue + '').length - 1);
  // item 需要是5的整数倍
  const res = Math.ceil(item / 5) * 5 * 5;
  return res;
}

/**
 * 获取图表数据最大值
 * @param array 图表需要显示的数据
 * @returns 最大值
 */
export function getSeriesItemMax(array) {
  const res = [];
  array.forEach((item) => {
    item.forEach((i, idx) => {
      if (!res[idx]) {
        if (isNaN(i / 1)) {
          res[idx] = 0;
        } else {
          res[idx] = i / 1;
        }
      } else {
        if (isNaN(i / 1)) {
          res[idx] += 0;
        } else {
          res[idx] += i / 1;
        }
      }
    });
  });
  return Math.max.apply(null, res);
}
export function formattedNumber(num) {
  if (num === null || num === undefined) {
    return '-';
  }
  const str = num?.toString();
  const reg = str.indexOf('.') > -1 ? /(\d)(?=(\d{3})+\.)/g : /(\d)(?=(?:\d{3})+$)/g;
  return str.replace(reg, '$1,');
}
// 适配给图表用的颜色
export const colorMap = ['rgba(91, 143, 249, 0.85)', 'rgba(90, 216, 166, 0.85)', 'rgba(119, 114, 241, 0.85)', 'rgba(246, 189, 22, 0.85)', 'rgba(232, 104, 74, 0.85)', 'rgba(109, 200, 236, 0.85)', 'rgba(146, 112, 202, 0.85)', 'rgba(255, 157, 77, 0.85)', 'rgba(73, 183, 182, 0.85)', 'rgba(255, 153, 195, 0.85)', 'rgba(128, 170, 255, 0.85)', 'rgba(189, 210, 253, 0.85)', 'rgba(199, 225, 102, 0.85)', 'rgba(189, 239, 219, 0.85)', 'rgba(237, 116, 173, 0.85)', 'rgba(184, 196, 224, 0.85)', 'rgba(255, 185, 78, 0.85)', 'rgba(251, 229, 162, 0.85)', 'rgba(255, 134, 106, 0.83)', 'rgba(246, 195, 183, 0.85)', 'rgba(140, 220, 251, 0.85)', 'rgba(182, 227, 245, 0.85)'];
export const echartsCommonOptions = {
  grid: {
    left: '5%',
    right: '5%',
    bottom: 80,
  },
};
// export function formattedNumber(num: number, fix = 2): string {
//   let val: number | string = num;
//   val = val.toFixed(fix) + ''; // 保留小数2位
//   val = '' + val; // 转换成字符串
//   let int = val.slice(0, fix * -1 - 1); // 拿到整数
//   const ext = val.slice(fix * -1 - 1); // 获取到小数
//   //每个三位价格逗号
//   int = int.split('').reverse().join(''); // 翻转整数
//   let temp = ''; // 临时变量
//   for (let i = 0; i < int.length; i++) {
//     temp += int[i];
//     if ((i + 1) % 3 == 0 && i != int.length - 1) {
//       temp += ','; // 每隔三个数字拼接一个逗号
//     }
//   }
//   temp = temp.split('').reverse().join(''); // 加完逗号之后翻转
//   temp = temp + ext; // 整数小数拼接
//   return temp; // 返回
// }

/**
 * 将数字转换为带中文单位的字符串
 * @param num :number 需要转换的数字
 * @param param1 :formattedNumberCNOptions 转换中可配置的选项
 * @returns 转换结果
 */
export function formattedNumberCN(num, { getUnits = 0 } = {}) {
  let result = 0;
  let units = '';
  if (num < 1000) {
    result = num;
  } else if (num < 10000 && num > 1000) {
    result = formattedNumber(num);
  } else {
    result = formattedNumber((num / 10000).toFixed(1));
    units = '万';
  }
  switch (getUnits) {
    case 1:
      return result;
    case 2:
      return units;
    default:
      return result + units;
  }
}

/***
 * 乘法，获取精确乘法的结果值
 * @param arg1
 * @param arg2
 * @returns
 */
function accMul(arg1, arg2) {
  let m = 0;
  let s1 = '';
  let s2 = '';
  try {
    s1 = arg1.toString();
    s2 = arg2.toString();
  } catch (e) {
    s1 = '0';
    s2 = '0';
  }
  try {
    m += s1.split('.')[1].length;
  } catch (e) {
    // console.warn(e);
  }
  try {
    m += s2.split('.')[1].length;
  } catch (e) {
    // console.warn(e);
  }
  return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
}
/**
 * 获取对应值的带单位的额度
 * @params data:number|string 需要转换的数字
 * @params toFixedLength:number 保留几位小数，默认值=1
 * @params returnType:number 返回值的类型（0=数字+单位，1=数字，2=单位），默认值=1
 * @returns 转换后的储存量，根据参数(returnType)返回不同的值
 * ps:先将获取到的数据统一转换为字节（b），然后再从字节层面统一向上计算
 */
export function getUsageResult(data, returnType = 0, toFixedLength = 1) {
  const dataSize = accMul(data, accMul(accMul(1024, 1024), 1024));
  let sizeTxt;
  let unit;
  if (dataSize / 1024 < 1) {
    sizeTxt = Math.ceil(dataSize * 100) / 100;
    unit = 'B';
  } else if (dataSize / 1024 / 1024 < 1) {
    sizeTxt = Math.ceil((dataSize / 1024) * 100) / 100;
    unit = 'KB';
  } else if (dataSize / 1024 / 1024 / 1024 < 1) {
    sizeTxt = Math.ceil((dataSize / 1024 / 1024) * 100) / 100;
    unit = 'MB';
  } else if (dataSize / 1024 / 1024 / 1024 / 1024 < 1) {
    sizeTxt = Math.ceil((dataSize / 1024 / 1024 / 1024) * 100) / 100;
    unit = 'GB';
  } else {
    sizeTxt = Math.ceil((dataSize / 1024 / 1024 / 1024 / 1024) * 100) / 100;
    unit = 'TB';
  }
  if (returnType == 1) {
    return sizeTxt.toFixed(toFixedLength);
  }
  if (returnType == 2) {
    return unit;
  }
  return sizeTxt.toFixed(toFixedLength) + unit;
}

// 复制内容到剪切板
export function handleCopy(text) {
  const oInput = document.createElement('input');
  oInput.style.border = '0 none';
  oInput.style.color = 'transparent';
  oInput.value = text;
  document.body.appendChild(oInput);
  oInput.select(); // 选择对象
  document.execCommand('Copy'); // 执行浏览器复制命令
  oInput.parentNode?.removeChild(oInput);
}
