import { yAxisMax, getSeriesItemMax, echartsCommonOptions, colorMap } from '../utils';

export const getDataSeries = (data) => {
  const getLinearColor = (index) => {
    return {
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: colorMap[index].replace('0.85)', '0.15)'), // 100% 处的颜色
        },
        {
          offset: 1,
          color: colorMap[index].replace('0.85)', '0)'), // 0% 处的颜色
        },
      ],
      global: false, // 缺省为 false
    };
  };
  let option = {};

  const chartsData = {
    xAxis: [],
    datas: {
      activeCount: [],
      userCount: [],
      userDayActiveCount: [],
      userEffectiveCount: [],
      userLoginCount: [],
      userMonthActiveCount: [],
    },
  };
  const ObjectSortArr = [
    // {
    //   key: 'userCount',
    //   name: '用户数（人）',
    //   yAxisIndex: 0,
    //   show: true,
    // },
    {
      key: 'userLoginCount',
      name: '登录用户数（人）',
      yAxisIndex: 0,
      show: true,
    },
    {
      key: 'userEffectiveCount',
      name: '激活用户数（人）',
      yAxisIndex: 0,
      show: true,
    },
    {
      key: 'userMonthActiveCount',
      name: '月活用户数（人）',
      yAxisIndex: 0,
      show: true,
    },
    {
      key: 'userDayActiveCount',
      name: '日活用户数（人）',
      yAxisIndex: 0,
      show: true,
    },
  ];
  data.forEach((x) => {
    chartsData.xAxis.push(x.formatTime.replace(/\s/, '\n'));
    for (const i in chartsData.datas) {
      chartsData.datas[i].push(x[i]);
    }
  });
  const series = [];
  // const ObjectSortArr = ['userCount', 'activeCount'];
  for (let i = 0; i < ObjectSortArr.length; i++) {
    const item = ObjectSortArr[i];
    const data = chartsData.datas[item.key];
    series.push({
      name: item.name,
      data,
      type: 'line',
      symbolSize: 10,
      showSymbol: false,
      areaStyle: { color: getLinearColor(i) },
      itemStyle: {
        borderWidth: 10,
      },
      yAxisIndex: item.yAxisIndex,
      lineStyle: {},
    });
  }
  let y1DataList = [];
  let y2DataList = [];
  series.forEach((item) => {
    if (item.yAxisIndex === 0) {
      y1DataList.push(item.data);
    } else {
      y2DataList.push(item.data);
    }
  });
  if (y1DataList.length === 0) {
    y1DataList = y2DataList;
  }
  if (y2DataList.length === 0) {
    y2DataList = y1DataList;
  }

  option = {
    color: colorMap,
    dataZoom: [
      {
        type: 'inside',
        start: (100 / 365) * 334, // 默认显示最近30天的数据
        end: 100,
      },
      {
        type: 'slider',
        left: '200px',
        right: '200px',
      },
    ],
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: chartsData.xAxis,
    },
    yAxis: [
      {
        position: `left`,
        type: 'value',
        name: `用户数（人）`,
        interval: yAxisMax(getSeriesItemMax(y1DataList)) / 5,
        max: yAxisMax(getSeriesItemMax(y1DataList)),
      },
      // {
      //   position: `right`,
      //   type: 'value',
      //   name: `活跃用户数（人）`,
      //   interval: yAxisMax(getSeriesItemMax(y2DataList)) / 5,
      //   max: yAxisMax(getSeriesItemMax(y2DataList)),
      // },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'none',
        label: {},
      },
      // alwaysShowContent: true,
      formatter: (params, ticket) => {
        let rowString = ``;
        params.forEach((x, i) => {
          rowString += `<div class="row">
              <p>
                <span class="dot-common" style="background-color:${colorMap[i]}"></span><span style="display:inline-block;min-width:50px">${x.data}</span>人
                <span style="margin-left:20px">${x.seriesName.replace('（人）', '')}</span>
              </p>
            </div>`;
        });
        return `
      <div class="tooltip-wrap">
        <div class="tooltip-content">
          ${rowString}
        </div>
        <div class="tooltip-footer">${params[0].axisValue}</div>
      </div>
      `;
      },
    },
    legend: {
      left: 'left',
      data: (() => {
        const legendArr = [];
        series.forEach((x) => {
          if (ObjectSortArr.find((y) => y.name === x.name)?.show) {
            legendArr.push(x.name);
          }
        });
        return legendArr;
      })(),
      itemHeight: 8,
    },
    series,
    ...echartsCommonOptions,
  };
  return option;
};
