<template>
  <div class="kl-menu">
    <div v-if="menuHasProject" :class="['project', !collapsed && 'line']">
      <jt-icon type="iconleft" class="exit-icon" @click="exitProject" />
      <a-select
        v-if="!collapsed"
        ref="select"
        v-model:value="selectedProject"
        :get-popup-container="
          (triggerNode) => {
            return triggerNode.parentNode;
          }
        "
        class="select-project"
        style="width: 147px"
        @change="changeMenu"
      >
        <a-select-option v-for="(item, index) in projects" :key="index" :value="item.id">
          <a-tooltip :get-popup-container="getPopupContainer">
            <template #title>{{ item.name }}</template>
            {{ item.name }}
          </a-tooltip>
        </a-select-option>
      </a-select>
    </div>
    <a-menu v-model:openKeys="openKeys" v-model:selectedKeys="activeKey" mode="inline" theme="light" class="menu" @click="handleClickItem">
      <a-menu-item v-for="item in menu.filter((x) => !(x.subs && x.subs.length > 0))" :key="item.link">
        <template #icon>
          <span class="icon iconfont" :class="item.icon"></span>
        </template>
        <span>{{ item.title }}</span>
      </a-menu-item>
      <a-sub-menu v-for="item in menu.filter((x) => x.subs && x.subs.length > 0)" :key="item.title">
        <template #icon>
          <span class="icon iconfont" :class="item.icon"></span>
        </template>
        <template #title>
          <span>{{ item.title }}</span>
        </template>
        <a-menu-item v-for="x in item.subs" :key="x.link">
          <template v-if="x.icon" #icon>
            <span class="icon iconfont" :class="x.icon"></span>
          </template>
          {{ x.title }}
        </a-menu-item>
      </a-sub-menu>
    </a-menu>
  </div>
</template>

<script setup>
import { computed, onMounted, watch, defineProps } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { LINK_FLAG } from '@/constants';
import { updateUrlAndRefresh } from '@/utils/index';

const props = defineProps({
  collapsed: {
    type: Boolean,
    required: true,
  },
  forcePath: {
    type: String,
    default: '',
  },
});

const route = useRoute();
const router = useRouter();
const store = useStore();
const menuHasProject = computed(() => store.state.menuHasProject);
const menu = computed(() => filterMenu());
const openKeys = computed(() => store.state.sideMenu.filter((x) => !x.link).map((x) => x.title));
const activeKey = computed(() => {
  if (props.forcePath) {
    return [props.forcePath];
  }
  return ['/' + route.path.split('/')[1]];
});
const selectedProject = computed(() => store.state.projectId);
const projects = computed(() => store.state.projectList);

onMounted(async () => {
  // 当访问运营管理平台时不触发当前接口
  if (store.state.menuHasPlatform) return;
  store.dispatch('getAllProjectList');
});

watch(
  () => store.state.projectId,
  async (projectId) => {
    if (projectId) {
      store.dispatch('getProjectPolicyById');
      store.dispatch('getProjectInfo');
      store.dispatch('getGpuCardMax');
      store.dispatch('initSideMenu');
    }
  },
  {
    immediate: true,
  }
);

const getPopupContainer = () => {
  const targetNode = document.querySelector('.select-project');
  return targetNode;
};

const filterMenu = () => {
  const sideMenu = store.state.sideMenu;
  const menus = JSON.parse(JSON.stringify(sideMenu));
  if (store.state.menuHasPlatform) {
    return menus.filter((item) => {
      const subs = item.subs;
      if (subs && subs.length) {
        return subs.some((sub) => sub.space === 'platform');
      }
      return item.space === 'platform';
    });
  }
  if (store.state.menuHasProject) {
    return menus.filter((item) => {
      const subs = item.subs;
      if (subs && subs.length) {
        return subs.some((sub) => sub.space === 'project');
      }
      return item.space === 'project';
    });
  }
  return menus.filter((item) => {
    const subs = item.subs;
    if (subs && subs.length) {
      return subs.some((sub) => sub.space === 'pool');
    }
    return item.space === 'pool';
  });
};
// 筛选选中的菜单
const filterChoosedMenu = (key) => {
  const menuList = JSON.parse(JSON.stringify(filterMenu()));
  const menuDta = menuList.find((item) => {
    const subs = item.subs;
    if (item.link === key) return item;
    if (subs && subs.length) {
      return subs.find((sub) => sub.link === key);
    }
  });
  if (menuDta.subs && menuDta.subs.length) {
    return menuDta.subs.find((sub) => sub.link === key);
  }
  return menuDta;
};

const handleClickItem = ({ key }) => {
  const { linkFlag, title } = filterChoosedMenu(key) || {};
  if (linkFlag === LINK_FLAG.RELATIVE) {
    if (title === '项目空间详情') {
      router.push('/project-space-details');
    } else {
      router.push(key);
    }
  } else {
    openInNewTab(key);
  }
  if (route.path === key || route.meta?.reRender === 1) {
    setTimeout(() => {
      store.commit('UPDATE_ROUTEVIEWREFRESHKEY');
    });
  }
};

const changeMenu = (value) => {
  updateUrlAndRefresh({ projectId: value }, false);
  removeTrainCurrentUser();
  const url = window.location.href.split('#')[0];
  const params = new URL(url).searchParams;
  const poolId = params.get('poolId');
  const projectId = params.get('projectId');
  if (poolId && projectId) {
    window.location.href = new URL(`${url}#/project-space-details`).href;
    window.location.reload();
  }
};

const exitProject = () => {
  router.push('/project-space');
};

const removeTrainCurrentUser = () => {
  sessionStorage.removeItem('trainDevCurrentUser');
  sessionStorage.removeItem('trainTaskCurrentUser');
};
</script>

<style lang="less" scoped>
.kl-menu {
  padding-left: 12px;
}
.project {
  display: flex;
  margin: 4px 4px 15px 0;
  text-align: center;
  &.line {
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(0, 20, 26, 0.08);
  }
  .exit-icon {
    border: 1px solid #d9d9d9;
    height: 32px;
    padding: 0 8px;
    color: rgba(0, 20, 26, 0.45);
    border-radius: 2px;
    background: @jt-color-white;
    &:hover {
      color: @jt-primary-color;
      border-color: @jt-primary-color;
    }
  }
  .select-project {
    margin-left: 6px;
    text-align: left;
  }
}
.menu {
  width: 192px;
  height: calc(100vh - 175px);
  border: none !important;
  background: @jt-background-color;
  overflow-y: auto;
}
:deep(.menu-icon) {
  font-size: 22px !important;
}
:deep(.ant-menu-light) {
  color: @jt-text-color-primary;
}
:deep(.ant-menu-inline.ant-menu-root .ant-menu-item),
:deep(.ant-menu-inline.ant-menu-root .ant-menu-submenu-title) {
  padding-left: 15px !important;
  margin-inline: 0;
}
:deep(.ant-menu-inline.ant-menu-root .ant-menu-item-only-child) {
  padding-left: 40px !important;
}
:deep(.ant-menu-item) {
  border-radius: 4px;
}
:deep(.ant-menu-item .ant-menu-item-icon),
:deep(.ant-menu-submenu-title .ant-menu-item-icon) {
  font-size: 22px;
}
:deep(.ant-menu-item-only-child > .ant-menu-title-content) {
  padding-left: 8px;
}
:deep(.ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline) {
  background: @jt-background-color;
}
:deep(.ant-menu-light .ant-menu-item-selected) {
  background-color: @jt-color-white;
  font-weight: 600;
}
:deep(.ant-menu-light .ant-menu-item-selected .ant-menu-item-icon) {
  background: -webkit-linear-gradient(#00cada, #00a2f4);
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 20px;
}
:deep(.ant-menu-item:not(.ant-menu-item-selected) .ant-menu-item-icon) {
  background: -webkit-linear-gradient(rgba(0, 20, 26, 0.25), rgba(0, 20, 26, 0.35));
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 20px;
}
:deep(.ant-menu-submenu-inline .ant-menu-item-icon) {
  background: -webkit-linear-gradient(rgba(0, 20, 26, 0.25), rgba(0, 20, 26, 0.35));
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 20px;
}
:deep(.ant-menu-submenu-inline.ant-menu-submenu-selected .ant-menu-item-icon) {
  background: -webkit-linear-gradient(#00cada, #00a2f4);
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 20px;
}
:deep(.ant-menu-light:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):hover),
:deep(.ant-menu-light:not(.ant-menu-horizontal) .ant-menu-submenu-title:hover) {
  background-color: rgba(0, 20, 26, 0.04);
}
:deep(.ant-menu-inline .ant-menu-item-only-child:not(.ant-menu-item-selected)) {
  color: @jt-text-color-primary-opacity07;
}
:deep(.ant-menu .ant-menu-submenu-inline:not(.ant-menu-submenu-selected) .ant-menu-submenu-title .ant-menu-item-icon),
:deep(.ant-menu .ant-menu-item:not(.ant-menu-item-selected) .ant-menu-item-icon),
:deep(.ant-menu-light .ant-menu-submenu-title:hover:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected) .ant-menu-item-icon) {
  color: @jt-text-color-primary-opacity05;
}
:deep(.ant-menu-light .ant-menu-submenu-selected > .ant-menu-submenu-title) {
  font-weight: 600;
}
</style>
