<template>
  <div class="dataset-drawer-wrap">
    <a-flex class="dataset-button-group" align="center" justify="space-between">
      <a-button type="primary" class="show-drawer-button" size="small" ghost @click="showDrawer">
        <PlusOutlined class="button-icon" />
        {{ props.buttonTitle }}
      </a-button>
      <div class="dataset-selected">
        已选数据集：<span>{{ selectedDatasetAll.length }}</span>
      </div>
    </a-flex>
    <dataset-selected-table ref="datasetSelectedTableRef" class="dataset-selected-table" :table-data="datasetSelectedTableData" @delete="datasetSelectedTableDelete" />

    <a-drawer v-model:open="drawerDisplay" :body-style="datasetDrawerBodyStyle" :mask-closable="false" :width="890" class="common-dataset-drawer" title="选择数据集" @afterOpenChange="afterDrawerOpenChange" @close="drawerCancel">
      <dataset-table v-model:customSelectedRowKeys="customSelectedRowKeys" v-model:presetSelectedRowKeys="presetSelectedRowKeys" v-model:shareSelectedRowKeys="shareSelectedRowKeys" :custom-table-data="customTableData" :preset-table-data="presetTableData" :share-table-data="shareTableData" />
      <template #footer>
        <a-flex style="height: 100%" justify="space-between" align="center">
          <div class="dataset-selected">
            已选数据集：<span>{{ selectedDatasetAll.length }}</span>
            <jt-icon v-if="selectedDatasetAll.length >= 1" type="iconshanchu1" class="iconfont clear-dataset" @click="clearDatasetSelected" />
          </div>
          <a-space style="padding-right: 12px">
            <a-button @click="drawerCancel">取消</a-button>
            <a-button type="primary" :class="[!selectedDatasetAll.length ? 'drawer-dataset-disabled' : 'kl-create-btn']" :disabled="!selectedDatasetAll.length" @click="drawerConfirm">确定</a-button>
          </a-space>
        </a-flex>
      </template>
    </a-drawer>
  </div>
</template>

<script setup>
import { ExclamationCircleFilled, QuestionCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';

import { datasetDrawerBodyStyle, DATASET_TYPES } from './constant.js';
import { datasetApi } from '@/apis';

import datasetTable from './datasetTable/index.vue';
import datasetSelectedTable from './datasetSelectedTable.vue';
import { message } from 'ant-design-vue';

const emits = defineEmits(['drawerConfirm', 'afterDrawerOpenChange']);
const props = defineProps({
  buttonTitle: {
    type: String,
    default: '选择',
  },
  filterCondition: {
    type: Object,
    default: () => {
      return {
        page: 0,
        pageSize: 0,
        isEnabled: 1,
      };
    },
  },
  pannelDisabled: {
    type: Boolean,
    default: false,
  },
  errorMsg: {
    type: String,
    default: '',
  },
});

const customSelectedRowKeys = defineModel('customSelectedRowKeys', { type: Array, default: () => [] });
const presetSelectedRowKeys = defineModel('presetSelectedRowKeys', { type: Array, default: () => [] });
const shareSelectedRowKeys = defineModel('shareSelectedRowKeys', { type: Array, default: () => [] });
const drawerDisplay = ref(false);
const datasetSelectedTableData = computed(() => {
  const customTableCurrentData = customTableData.value.filter((x) => currentSelectKeys.value.customSelectedRowKeys?.includes(x.datasetVersionId));
  const presetTableCurrentData = presetTableData.value.filter((x) => currentSelectKeys.value.presetSelectedRowKeys?.includes(x.presetId));
  const shareTableCurrentData = shareTableData.value.filter((x) => currentSelectKeys.value.shareSelectedRowKeys?.includes(x.datasetVersionId));
  return { customTableData: customTableCurrentData, presetTableData: presetTableCurrentData, shareTableData: shareTableCurrentData };
});

const datasetSelectedTableRef = ref(null);
const customTableData = ref([]);
const presetTableData = ref([]);
const shareTableData = ref([]);

// 当前已经确定被选到的所有数据集
// const currentDatasetAll = computed(() => {
//   return [...(currentSelectKeys.value.customSelectedRowKeys || []), ...(currentSelectKeys.value.presetSelectedRowKeys || []), ...(currentSelectKeys.value.shareSelectedRowKeys || [])];
// });
// 当前所有已选择数据集（包含未点击确定的）
const selectedDatasetAll = computed(() => {
  return [...(customSelectedRowKeys.value || []), ...(presetSelectedRowKeys.value || []), ...(shareSelectedRowKeys.value || [])];
});
const currentSelectKeys = ref({
  customSelectedRowKeys: [],
  presetSelectedRowKeys: [],
  shareSelectedRowKeys: [],
}); // 初始的被选中的项

const resetSelectKeys = () => {
  nextTick(() => {
    currentSelectKeys.value.customSelectedRowKeys = customSelectedRowKeys.value;
    currentSelectKeys.value.presetSelectedRowKeys = presetSelectedRowKeys.value;
    currentSelectKeys.value.shareSelectedRowKeys = shareSelectedRowKeys.value;
  });
  // currentSelectKeys.value = {
  //   customSelectedRowKeys: customSelectedRowKeys.value,
  //   presetSelectedRowKeys: presetSelectedRowKeys.value,
  // };
};
const showDrawer = () => {
  if (props.pannelDisabled) {
    message.error(props.errorMsg);
    return;
  }
  drawerDisplay.value = true;
  resetSelectKeys();
};
const drawerCancel = () => {
  customSelectedRowKeys.value = [...(currentSelectKeys.value.customSelectedRowKeys || [])];
  presetSelectedRowKeys.value = [...(currentSelectKeys.value.presetSelectedRowKeys || [])];
  shareSelectedRowKeys.value = [...(currentSelectKeys.value.shareSelectedRowKeys || [])];
  drawerDisplay.value = false;
};
const drawerConfirm = () => {
  drawerDisplay.value = false;
  resetSelectKeys();
  datasetSelectedTableRef.value.changePageNum(1);
};

const datasetSelectedTableDelete = async (record) => {
  switch (record.__dataset_type) {
    case DATASET_TYPES.PRESET:
      presetSelectedRowKeys.value = presetSelectedRowKeys.value.filter((x) => x !== record.presetId);
      break;
    case DATASET_TYPES.CUSTOM:
      customSelectedRowKeys.value = customSelectedRowKeys.value.filter((x) => x !== record.datasetVersionId);
      break;
    case DATASET_TYPES.SHARE:
      shareSelectedRowKeys.value = shareSelectedRowKeys.value.filter((x) => x !== record.datasetVersionId);
      break;
  }
  await nextTick();
  resetSelectKeys();
};

// 将当前所选数据集和数据集列表进行比对，防止出现列表中的数据集被删除了，但是所选的还存在，导致后续选择数量和信息对不上的问题
const updateSelectedRowKeys = () => {
  const customTableDataKeys = customTableData.value.map((x) => x.datasetVersionId);
  const shareTableDataKeys = shareTableData.value.map((x) => x.datasetVersionId);
  const presetTableDataKeys = presetTableData.value.map((x) => x.presetId);

  customSelectedRowKeys.value = customSelectedRowKeys.value.filter((x) => customTableDataKeys.includes(x));
  shareSelectedRowKeys.value = shareSelectedRowKeys.value.filter((x) => shareTableDataKeys.includes(x));
  presetSelectedRowKeys.value = presetSelectedRowKeys.value.filter((x) => presetTableDataKeys.includes(x));
};
const getTableData = async () => {
  const params = {
    ...props.filterCondition,
  };

  const getDatasetNoShareListRef = await datasetApi.getDatasetNoShareList({ ...params, modelName: '0' });
  customTableData.value =
    getDatasetNoShareListRef.data?.list?.map((x) => {
      x.__dataset_type = DATASET_TYPES.CUSTOM;
      return x;
    }) ?? [];

  const getDatasetPresetListRef = await datasetApi.getDatasetPresetList(params);
  presetTableData.value =
    getDatasetPresetListRef.data?.list?.map((x) => {
      x.__dataset_type = DATASET_TYPES.PRESET;
      return x;
    }) ?? [];

  const getDatasetShareListRef = await datasetApi.getDatasetShareList({ ...params, modelName: '0' });
  shareTableData.value =
    getDatasetShareListRef.data?.list?.map((x) => {
      x.__dataset_type = DATASET_TYPES.SHARE;
      return x;
    }) ?? [];

  updateSelectedRowKeys();
  resetSelectKeys();

  return Promise.resolve({ presetTableData: presetTableData.value, shareTableData: shareTableData.value, customTableData: customTableData.value });
};

const getSelectedItem = () => {
  const selectedItem = {
    keys: toRaw(currentSelectKeys.value),
    items: toRaw(datasetSelectedTableData.value),
  };
  return selectedItem;
};
const afterDrawerOpenChange = (open) => {
  emits('afterDrawerOpenChange', open);
};
const clearDatasetSelected = () => {
  customSelectedRowKeys.value = [];
  presetSelectedRowKeys.value = [];
  shareSelectedRowKeys.value = [];
};

watch(
  () => props.filterCondition,
  () => {
    getTableData();
  }
);
watch(
  () => drawerDisplay.value,
  (state) => {
    if (state) {
      getTableData();
    }
  }
);

onMounted(() => {
  getTableData();
});
defineExpose({
  getSelectedItem,
  resetSelectKeys,
  getTableData,
});
</script>

<style lang="less" scoped>
.show-drawer-button {
  width: 80px;
  height: 32px;
  .button-icon {
    font-size: 12px;
  }
}
.dataset-selected {
  margin-right: 20px;
  span {
    color: @jt-primary-color;
  }
  .clear-dataset {
    margin-left: 12px;
    cursor: pointer;
    color: inherit;
    font-size: 14px;
  }
}
.dataset-selected-table {
  margin-top: 16px;
}
.drawer-dataset-disabled {
  color: @jt-color-white;
  border-color: transparent;
  background: rgba(0, 20, 26, 0.15);
}
</style>
<style lang="less">
.common-dataset-drawer {
  ::-webkit-scrollbar-track {
    box-shadow: none;
  }
}
</style>
