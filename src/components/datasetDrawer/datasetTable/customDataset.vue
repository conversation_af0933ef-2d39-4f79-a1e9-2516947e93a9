<template>
  <div class="custom-dataset-table">
    <a-table :scroll="datasetTableScroll" row-key="datasetVersionId" :pagination="false" :row-selection="{ selectedRowKeys: selectedRowKeys, onSelect: onSelectChange, onSelectAll: onSelectAll }" :columns="columns" :data-source="tableDataSource">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'datasetName'">
          <a-tooltip placement="top" :title="record.datasetName">
            <span class="ellipsis-text cursor">
              {{ record.datasetName }}
            </span>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'classification'">
          <a-tooltip placement="top" :title="DATASET_TEXT.classification[record.classification]">
            <span class="ellipsis-text cursor">
              {{ DATASET_TEXT.classification[record.classification] ?? '--' }}
            </span>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'datasetType'">
          <a-tooltip placement="top" :title="DATASET_TEXT.datasetType[record.datasetType]">
            <span class="ellipsis-text cursor">
              {{ DATASET_TEXT.datasetType[record.datasetType] ?? '--' }}
            </span>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'datasetDescription'">
          <a-tooltip placement="top" :title="record.datasetDescription">
            <span class="ellipsis-text cursor">
              {{ record.datasetDescription }}
            </span>
          </a-tooltip>
        </template>
      </template>
    </a-table>
    <jt-pagination :total="props.tableData.length" :page-num="pageParams.pageNum" :page-size="pageParams.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
  </div>
</template>
<script setup>
import { datasetTableScroll, DATASET_TEXT } from '../constant.js';
const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
});

const columns = [
  {
    title: '数据集名称',
    dataIndex: 'datasetName',
    ellipsis: true,
    width: '20%',
  },
  // {
  //   title: '数据集分类',
  //   dataIndex: 'classification',
  //   ellipsis: true,
  // },
  {
    title: '数据类型',
    dataIndex: 'datasetType',
    ellipsis: true,
  },
  {
    title: '描述',
    dataIndex: 'datasetDescription',
    ellipsis: true,
  },
  {
    title: '版本',
    dataIndex: 'datasetVersion',
  },
  {
    title: '创建时间',
    dataIndex: 'updateTime',
    sorter: (a, b) => new Date(a.updateTime).getTime() - new Date(b.updateTime).getTime(),
    width: 200,
  },
];

const selectedRowKeys = defineModel('selectedRowKeys', { type: Array, default: () => [] });

const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const tableDataSource = computed(() => {
  return props.tableData.filter((x, i) => i >= (pageParams.value.pageNum - 1) * pageParams.value.pageSize && i < pageParams.value.pageNum * pageParams.value.pageSize);
});

const changePageSize = (pageSize) => {
  pageParams.value.pageSize = +pageSize;
  changePageNum(1);
};
const changePageNum = (page) => {
  pageParams.value.pageNum = +page;
};
const onSelectChange = (record, selected) => {
  if (selected) {
    selectedRowKeys.value = [...new Set(selectedRowKeys.value.concat([record.datasetVersionId]))];
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter((x) => x !== record.datasetVersionId);
  }
};
const onSelectAll = (selected, selectedRows, changeRows) => {
  const ids = [];
  for (const x of changeRows) {
    if (x) {
      ids.push(x.datasetVersionId);
    }
  }
  if (selected) {
    selectedRowKeys.value = [...new Set(selectedRowKeys.value.concat(ids))];
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter((x) => !ids.includes(x));
  }
};
</script>
