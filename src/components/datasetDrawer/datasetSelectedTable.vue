<template>
  <div class="dataset-selected-table">
    <a-table :pagination="false" :columns="columns" :class="{ empty: !dataListComputed.length }" :data-source="dataListComputed" @change="onTableChange">
      <template #emptyText>
        <light-empty title="暂无挂载数据集" />
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'dataSource'">
          <jt-tag rounded="small" :color="getDataSource(record).color">{{ getDataSource(record).text }}</jt-tag>
        </template>
        <template v-if="column.dataIndex === 'datasetName'">
          <span class="ellipsis-text" :class="{ 'delete-text': datasetDeleteStatus(record) }">
            {{ record.datasetName }}
          </span>
          <!-- 保留内容，如果后续需要具体文案时可以直接使用 -->
          <!-- <a-tooltip placement="top">
          <template v-if="datasetDeleteStatus(record)" #title>
            {{ datasetDeleteStatus(record) }}
          </template>
          <span class="ellipsis-text" :class="{ 'delete-text': datasetDeleteStatus(record) }">
            {{ record.datasetName }}
          </span>
        </a-tooltip> -->
        </template>
        <template v-if="column.dataIndex === 'datasetVersion'">
          <span class="ellipsis-text" :class="{ 'delete-text': datasetDeleteStatus(record) }">
            {{ record.datasetVersion ?? '--' }}
          </span>
        </template>
        <template v-if="column.dataIndex === 'datasetDescription'">
          <a-tooltip placement="top" :title="record.datasetDescription || record.description">
            <span class="ellipsis-text">
              {{ record.datasetDescription || record.description }}
            </span>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'options'">
          <a-button class="table-button" ghost type="link" @click="deleteItem(record)">删除</a-button>
        </template>
      </template>
    </a-table>
    <jt-pagination :total="dataListCollection.length" :page-num="getTableDataParams.pageNum" :page-size="getTableDataParams.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
  </div>
</template>
<script setup>
import LightEmpty from '@/views/management-center/activity-manage/components/light-empty.vue';
import { DATASET_TYPES, DATASET_ATTR } from './constant.js';

const emits = defineEmits(['delete']);
const props = defineProps({
  tableData: {
    type: Object,
    default: () => {
      return { customTableData: [], presetTableData: [], shareTableData: [] };
    },
  },
  showOptions: {
    type: Boolean,
    default: true,
  },
});
const getTableDataParams = ref({
  pageNum: 1,
  pageSize: 5,
});
const changePageSize = (pageSize) => {
  getTableDataParams.value.pageSize = pageSize;
  changePageNum(1);
};
const changePageNum = (pageNum) => {
  getTableDataParams.value.pageNum = pageNum;
};

const columns = computed(() => {
  const dataSourceFilters = [];
  for (const i in DATASET_TYPES) {
    dataSourceFilters.push({
      text: DATASET_ATTR[DATASET_TYPES[i]].text,
      value: DATASET_TYPES[i],
    });
  }
  const temp = [
    {
      title: '数据集来源',
      dataIndex: 'dataSource',
      width: '20%',
      filters: dataSourceFilters,
      onFilter: (value, record) => {
        return record.__dataset_type === value;
      },
    },
    {
      title: '数据集名称',
      dataIndex: 'datasetName',
      ellipsis: true,
    },
    {
      title: '数据集版本',
      dataIndex: 'datasetVersion',
    },
    {
      title: '数据集描述',
      dataIndex: 'datasetDescription',
      ellipsis: true,
    },
  ];
  if (props.showOptions) {
    temp.push({
      title: '操作',
      dataIndex: 'options',
    });
  }
  return temp;
});

const dataListCollection = computed(() => {
  return [...props.tableData.customTableData, ...props.tableData.presetTableData, ...props.tableData.shareTableData].filter((x) => {
    if (tableFilterObj.value.dataSource) {
      return tableFilterObj.value.dataSource.includes(x.__dataset_type);
    }
    return true;
  });
});
const dataListComputed = computed(() => {
  return dataListCollection.value.filter((x, i) => i >= (getTableDataParams.value.pageNum - 1) * getTableDataParams.value.pageSize && i < getTableDataParams.value.pageNum * getTableDataParams.value.pageSize);
});

let tableFilterObj = ref({});
const onTableChange = (pagination, filters) => {
  tableFilterObj.value = { ...filters };
  changePageNum(1);
};
const getDataSource = (record) => {
  return {
    color: DATASET_ATTR[record.__dataset_type].color,
    text: DATASET_ATTR[record.__dataset_type].text,
  };
};
const deleteItem = (record) => {
  emits('delete', record);
};
const datasetDeleteStatus = (record) => {
  // 保留内容，如果后续需要具体文案时可以直接使用
  switch (record.__dataset_type) {
    case DATASET_TYPES.PRESET:
      return record.status === 1 ? '' : '数据集已停用';
    case DATASET_TYPES.CUSTOM:
    case DATASET_TYPES.SHARE:
      return record.status === 0 || record.isEnabled === 0 || record.isCancel === 1 ? '数据集或版本已被停用、删除或取消分享' : '';
  }
  return '';
};

defineExpose({
  changePageNum,
});
</script>

<style lang="less" scoped>
.table-button {
  height: auto;
  padding: 0;
}
:deep(.ant-table-tbody) {
  .ant-table-row.ant-table-row-level-0 {
    height: 45px;
  }
}
</style>
