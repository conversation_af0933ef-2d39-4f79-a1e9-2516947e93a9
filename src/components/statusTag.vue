<template>
  <a-tag :color="color">{{ status }}</a-tag>
</template>

<script setup>
import { defineProps, computed } from 'vue';

const props = defineProps({
  status: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    default: '',
  },
});

const statusColorMap = {
  运行中: 'blue',
  启动中: 'orange',
  停止中: 'orange',
  停止: 'default',
  成功: 'success',
  失败: 'error',
  重试中: 'error',
  排队中: 'orange',
  快照中: 'orange',
};

const color = computed(() => props.color || statusColorMap[props.status]);
</script>
