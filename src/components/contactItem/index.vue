<template>
  <div>
    {{ (visible ? realMessage || hideMessage : hideMessage) || '--' }} 
    <jt-icon v-if="hideMessage" class="eye-icon" :type="visible ? 'iconeye' : 'icona-bianzu16beifen4'" @click="changeMessage" />
  </div>
</template>

<script setup>
import { requestWithProjectId } from '@/request/index';

const props = defineProps({
  hideMessage: {
    type: String,
    default: ''
  },
  url: {
    type: String,
    required: true
  },
  params: {
    type: Object,
    default: () => {
      return {};
    }
  }
})

const visible = ref(false);
const realMessage = ref('');

const changeMessage = async () => {
  visible.value =!visible.value;
  if (!realMessage.value) {
    const res = await requestWithProjectId.GET(props.url, props.params);
    // 返回参数待适配
    realMessage.value = res.data;
  }
}

</script>

<style lang="less" scoped>
  .eye-icon {
    cursor: pointer;
    margin-left: 8px;
    &:hover {
      color: @jt-primary-color;
    }
  }
</style>