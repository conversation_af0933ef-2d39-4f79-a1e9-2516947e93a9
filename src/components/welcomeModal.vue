<template>
  <a-modal v-model:open="open" class="welcome-modal-wrap" :footer="null" :class="currentModalContent.backgroundImage" @ok="handleOk">
    <h5>{{ currentModalContent.title }}</h5>
    <p>{{ currentModalContent.content }}</p>
    <a-select v-model:value="projectName" class="project-selector" placeholder="请选择" show-search :options="options" :option-filter-prop="'label'"> </a-select>
    <a-space>
      <a-tooltip v-if="!canCreate" placement="top">
        <template #title>{{ errorMessage }}</template>
        <a-button disabled class="button disabled-btn">新建项目空间</a-button>
      </a-tooltip>
      <a-button v-else class="button" @click="createNew">新建项目空间</a-button>
      <a-button v-if="isZero" disabled class="button disabled-btn">进入{{ currentModalContent.name }}</a-button>
      <a-button v-else class="kl-create-btn button" type="primary" @click="handleClick">进入{{ currentModalContent.name }}</a-button>
    </a-space>
  </a-modal>
</template>
<script setup>
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { updateUrlAndRefresh } from '@/utils/index';
import { enableCreateProject } from '@/apis/projectSpace';

const store = useStore();
const router = useRouter();

const modalContents = {
  dataCleaning: {
    title: '欢迎使用数据清洗！',
    content: '一站式数据清洗工具，提供数据清洗任务创建及管理，支持异常清洗、过滤、去隐私等，提升数据质量',
    url: '/data-cleaning',
    name: '数据清洗',
    backgroundImage: 'sjqx',
  },
  trainDev: {
    title: '欢迎使用开发环境！',
    content: '提供Jupyter、VSCode在线集成开发环境（IDE）及Shell终端，支持在云端进行算法开发和模型训练',
    url: '/train-dev',
    name: '开发环境',
    backgroundImage: 'xlrw',
  },
  modelManage: {
    title: '欢迎使用模型管理！',
    content: '灵活支持多厂家，多框架模型，您可将平台训练或第三方模型导入平台进行统一托管',
    url: '/modelManage',
    name: '模型管理',
    backgroundImage: 'mxgl',
  },
  mirrorManage: {
    title: '欢迎使用镜像管理！',
    content: '平台为您提供了大量的预置镜像，也可选择将自己的镜像迁移到平台，或者在平台的模型训练环节构建自定义镜像',
    url: '/mirrorManage',
    name: '镜像管理',
    backgroundImage: 'jxgl',
  },
  serviceManage: {
    title: '欢迎使用推理服务！',
    content: '提供将推理服务为在线推理服务的能力，支持多种推理框架及部署方式，支撑在线推理场景中的多元应用诉求',
    url: '/service-manage',
    name: '推理服务',
    backgroundImage: 'tlfw',
  },
  fileManage: {
    title: '欢迎使用文件管理！',
    content: '平台为您提供对象存储文件管理、普通文件存储和高性能文件存储管理的能力，可对训练、推理所需和产生的文件进行管理',
    url: '/file-manage',
    name: '文件管理',
    backgroundImage: 'wjgl',
  },
  dataSet: {
    title: '欢迎使用数据集管理！',
    content: '统一纳管用于模型训练和评估的数据集，支持版本管理、数据预览、导入导出等',
    url: '/dataset',
    name: '数据集管理',
    backgroundImage: 'sjjgl',
  },
  // prompt-engineering: {
  //   title: '欢迎使用数据集管理！',
  //   content: '统一纳管用于模型训练和评估的数据集，支持版本管理、数据预览、导入导出等',
  //   url: '/dataset',
  //   name: '数据集管理',
  //   backgroundImage: 'sjjgl',
  // },
};

const open = defineModel('open', { required: true, default: false });
const props = defineProps({
  type: {
    type: [String, null],
    default: '',
    validator(value) {
      const COLORS_ENUM = ['trainDev', 'modelManage', 'mirrorManage', 'serviceManage', 'fileManage', null];
      return COLORS_ENUM.includes(value);
    },
  },
});
const emits = defineEmits(['toDetail']);

const projectName = ref(undefined);
const options = ref([]);
const originOptions = ref([]);
// const modalContainer = () => document.querySelector('.modal-wrap');
const currentModalContent = ref({});
const canCreate = ref(false);
const errorMessage = ref('');

const isZero = computed(() => store.state.projectList.length === 0);

const handleOk = (e) => {
  open.value = false;
};
const getCanCreate = () => {
  enableCreateProject().then((res) => {
    if (res.code === 0) {
      canCreate.value = true;
      return;
    }
    if (res.code === 130121) {
      errorMessage.value = '您暂无创建项目空间权限，请联系平台管理员申请';
    } else {
      errorMessage.value = '您创建的项目空间数量已达上限 请删除项目空间后再试';
    }
    canCreate.value = false;
  });
};
const handleClick = () => {
  updateUrlAndRefresh({ projectId: projectName.value });
  store.commit('UPDATE_MENU_HAS_PROJECT', true);
  emits('toDetail', currentModalContent.value.url);
};
const createNew = () => {
  router.push({
    path: '/project-space/create',
  });
};

watch(
  () => props.open,
  (state) => {
    if (state) {
      currentModalContent.value = modalContents[props.type];
      getCanCreate();
    }
  }
);
watch(
  () => store.state.projectList,
  (val) => {
    if (val.length > 0) {
      originOptions.value = val;
      options.value =
        originOptions.value.map((item) => {
          return {
            label: item.name,
            value: item.id,
          };
        }) || [];
      projectName.value = options.value.length > 0 ? options.value[0].value : undefined;
    }
  },
  {
    immediate: true,
  }
);
</script>

<style lang="less">
.welcome-modal-wrap {
  .ant-modal-content {
    width: 579px;
    padding: 40px 32px;
    background-size: 100%;
    background-repeat: no-repeat;
  }
  &.ant-modal-body {
    margin-top: 0;
  }
  &.xlrw {
    .ant-modal-content {
      background-image: url('~@/assets/images/welcome-modal/bg-xlrw.png');
    }
  }
  &.mxgl {
    .ant-modal-content {
      background-image: url('~@/assets/images/welcome-modal/bg-mxgl.png');
    }
  }
  &.jxgl {
    .ant-modal-content {
      background-image: url('~@/assets/images/welcome-modal/bg-jxgl.png');
    }
  }
  &.tlfw {
    .ant-modal-content {
      background-image: url('~@/assets/images/welcome-modal/bg-tlfw.png');
    }
  }
  &.wjgl {
    .ant-modal-content {
      background-image: url('~@/assets/images/welcome-modal/bg-wjgl.png');
    }
  }
  &.sjqx {
    .ant-modal-content {
      background-image: url('~@/assets/images/welcome-modal/bg-sjqx.png');
    }
  }
  &.sjjgl {
    .ant-modal-content {
      background-image: url('~@/assets/images/welcome-modal/bg-sjjgl.png');
    }
  }
  h5 {
    font-weight: @jt-font-weight-medium;
    font-size: 20px;
    color: @jt-text-color-primary;
    line-height: 30px;
    margin-bottom: 12px;
  }
  p {
    width: 340px;
    font-size: @jt-font-size-lg;
    color: @jt-text-color-primary-opacity07;
    line-height: 24px;
    margin-bottom: 20px;
  }
  .project-selector {
    display: block;
    height: 32px;
    width: 248px;
    margin-bottom: 32px;
    .ant-select-selector {
      padding-left: 84px;
      position: relative;
      &::before {
        position: absolute;
        top: 4px;
        left: 12px;
        content: '\9879\76ee\7a7a\95f4\ff1a';
        font-size: 14px;
        font-weight: 400;
        color: #a0a6ab;
      }
    }
    .ant-select-selection-search {
      padding-left: 70px;
    }
  }
  .button {
    //width: 120px;
    height: 32px;
    &:not(.kl-create-btn, .disabled-btn) {
      border-color: @jt-primary-color;
      color: @jt-primary-color;
    }
  }
}
</style>
