<template>
  <a-button class="icon-reload-button" :disabled="disabled">
    <template #icon>
      <jt-icon type="iconshuaxin" class="iconfont" />
    </template>
  </a-button>
</template>

<script>
export default {
  name: 'ReloadIconBtn',
  props: {
    disabled: {
      type: <PERSON><PERSON>an,
      default: false,
    },
  },
};
</script>

<style lang="less" scoped>
.icon-reload-button {
  width: 32px;
  height: 32px;
}
.iconfont {
  font-size: 14px;
  color: rgba(0, 20, 26, 0.45);
}
</style>
