<template>
  <micro-app :name="appName || moduleName" :url="url" :data="microAppData"></micro-app>
</template>

<script>
import microApp from '@micro-zoe/micro-app';
import { portalPrefixUrl } from '@/config';
const prefix = process.env.VUE_APP_PORTAL_ROUTE_PREFIX ? '/kunlun' : portalPrefixUrl;
const url = process.env.NODE_ENV === 'development' ? 'http://localhost:8089/common-components' : `${window.location.origin}${prefix}/common-components`;
export default {
  name: 'MicroComponents',
  props: {
    appName: {
      type: String,
      default: undefined,
    },
    moduleName: {
      type: String,
      default: undefined,
    },
    data: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      url,
    };
  },
  computed: {
    microAppData() {
      return {
        moduleName: this.moduleName,
        data: {
          ...this.data,
        },
      };
    },
  },
  mounted() {
    microApp.addDataListener(this.moduleName, (remoteData) => {
      if (remoteData.isEvent) {
        this.$emit(remoteData.detail.type, remoteData.detail.data);
      }
    });
  },
};
</script>
