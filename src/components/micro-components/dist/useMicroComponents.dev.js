"use strict";

var _microApp = _interopRequireDefault(require("@micro-zoe/micro-app"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

_microApp["default"].start({
  fetch: function fetch(url, options) {
    var innerUrl = url; // 对url做一些兼容，框架会自动在url后面加上'/',然后就会导致资源请求不到，同时就会导致请求的静态资源多了一层'common-components'

    if (url.slice(-1) === '/') {
      innerUrl = url.slice(0, -1);
    }

    var reg = new RegExp('/common-components/common-components/');

    if (reg.test(url)) {
      innerUrl = url.replace('common-components/', '');
    }

    return window.fetch(innerUrl, Object.assign(options)).then(function (res) {
      return res.text();
    });
  }
});