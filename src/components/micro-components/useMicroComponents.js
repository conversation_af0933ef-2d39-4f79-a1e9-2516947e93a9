import microApp from '@micro-zoe/micro-app';

microApp.start({
  fetch(url, options) {
    let innerUrl = url;
    // 对url做一些兼容，框架会自动在url后面加上'/',然后就会导致资源请求不到，同时就会导致请求的静态资源多了一层'common-components'
    if (url.slice(-1) === '/') {
      innerUrl = url.slice(0, -1);
    }
    const reg = new RegExp('/common-components/common-components/');
    if (reg.test(url)) {
      innerUrl = url.replace('common-components/', '');
    }
    return window.fetch(innerUrl, Object.assign(options)).then((res) => {
      return res.text();
    });
  },
});
