<template>
  <div>
    <a-layout class="layout">
      <jt-header :collapsed="collapsed" @toggleCollapsed="toggleCollapsed"></jt-header>
      <a-layout class="layout-main" :style="{ width: '100%', 'min-width': `${collapsed ? 1360 : 1240}px` }">
        <a-layout-sider v-if="!isFullScreenRoute" v-model:collapsed="collapsed" class="sider" :collapsed-width="50" :trigger="null" collapsible>
          <!-- <a v-if="collapsed" class="logo-container-collapsed" :href="linkUrl">
            <img style="width: 35px" :src="collapsedLogoUrl" alt="" />
          </a>
          <a v-else class="logo-container" :href="linkUrl">
            <img v-if="logoUrl" style="width: 126px; height: 26px" :src="logoUrl" alt="" />
          </a> -->
          <p v-if="!collapsed" class="title" :style="{ marginLeft: isNotPool ? '12px' : '24px' }">{{ '我的项目' || title }}</p>
          <side-menu :collapsed="collapsed" :force-path="props.forcePath"></side-menu>
        </a-layout-sider>
        <a-layout-content :style="{ width: `calc(100% - ${styleContentLeftWidth}px)`, left: `${styleContentLeftWidth}px`, 'overflow-y': 'auto', height: 'calc(100vh - 50px)' }">
          <slot></slot>
        </a-layout-content>
      </a-layout>
      <div class="top-bg"></div>
    </a-layout>
  </div>
</template>
<script setup>
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useStore } from 'vuex';
import jtHeader from './header.vue';
import sideMenu from './sideMenu.vue';
import iconTop from '@/assets/images/logo.png';
import iconTopFold from '@/assets/images/logo-fold.png';

const store = useStore();
const collapsed = ref(false);
const isNotPool = computed(() => {
  return store.state.poolList.length === 0;
});
const title = computed(() => {
  return isNotPool.value ? store.state.layoutConfig?.subOverviewTab : store.state.layoutConfig?.subTab;
});
const logoUrl = computed(() => store.state.layoutConfig?.iconTop || iconTop);
const collapsedLogoUrl = computed(() => store.state.layoutConfig?.iconTopFold || iconTopFold);
const linkUrl = '/';
const isFullScreenRoute = ref(false);
const route = useRoute();
const props = defineProps({
  forcePath: {
    type: String,
    default: '',
  },
});

const toggleCollapsed = () => {
  collapsed.value = !collapsed.value;
};

const styleContentLeftWidth = computed(() => {
  if (isFullScreenRoute.value) {
    return 0;
  }
  return collapsed.value ? 50 : 200;
});

watch(
  () => route.meta.fullscreen,
  (value) => {
    isFullScreenRoute.value = value;
  }
);
</script>

<style lang="less" scoped>
.layout {
  height: fit-content;
  min-height: 100vh;
  background: none;
  > :deep(.ant-layout) {
    padding-top: 50px;
    background-color: @jt-background-color;
    background-image: url('~@/assets/images/background.png');
    background-size: cover;
    .ant-layout-content {
      z-index: 2;
      position: absolute;
      left: 200px;
      width: calc(100% - 200px);
    }
  }
}
.top-bg {
  position: absolute;
  top: 0;
  right: 0;
  width: 880px;
  height: 220px;
  background: url('~@/assets/images/bg-top.png');
  z-index: 1;
  background-size: cover;
}
.sider {
  position: relative;
  height: 100%;
  color: #121f2c;
  background: url('~@/assets/images/background.png');
  background-size: cover;
  z-index: 99;
  :deep(ul.ant-menu) {
    background-color: transparent !important;
  }
}
.layout-main {
  transition: all 0.3s;
}
.logo-container-collapsed,
.logo-container {
  height: 50px;
  background-color: @jt-color-white;
  cursor: pointer;
  display: block;
}
.logo-container,
.logo-container-collapsed {
  line-height: 50px;
  text-align: center;
  img {
    vertical-align: middle;
    border-style: none;
  }
}

.title {
  margin: 0;
  margin-left: 24px;
  width: 176px;
  height: 50px;
  color: @jt-text-color-primary-opacity07;
  font-size: @jt-font-size-lg;
  font-weight: 600;
  line-height: 50px;
}
</style>
