<template>
  <a-modal v-model:open="open" class="create-modal" :footer="null" width="500px">
    <template #title>
      <p class="modal-title">申请新建项目空间</p>
    </template>
    <a-form ref="formRef" :model="formState" :rules="rules" :label-col="labelCol">
      <!-- :colon="true" -->
      <a-form-item ref="requireName" label="需求名称：：" name="reqTitle">
        {{ props.applicationDetail.reqTitle }}
      </a-form-item>
      <a-form-item label="需求描述：：" name="reqDesc">
        {{ props.applicationDetail.reqDesc }}
      </a-form-item>
      <a-form-item label="使用资源池：：" name="poolName">
        {{ props.applicationDetail.poolName }}
      </a-form-item>
      <a-form-item label="姓名：：" name="fullName">
        {{ props.applicationDetail.fullName }}
      </a-form-item>
      <a-form-item label="工作单位：：" name="company">
        {{ props.applicationDetail.company }}
      </a-form-item>
    </a-form>
    <a-space style="display: flex; justify-content: right; margin-top: 20px">
      <a-button :loading="confirmLoding" @click="handleOk">撤回申请</a-button>
      <a-button type="primary" @click="handleCancel()">关闭</a-button>
    </a-space>
  </a-modal>
</template>
<script setup>
import { computed, onMounted } from 'vue';
import { useStore } from 'vuex';

import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import { createVNode, defineProps } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { projectSpaceApi } from '@/apis';

const store = useStore();

const emits = defineEmits(['cancel', 'ok']);
const open = defineModel('open', { type: Boolean, default: false });
const formRef = ref();

const labelCol = { style: { width: '90px' } };

const formState = ref({});
const props = defineProps({
  applicationDetail: {
    type: Object,
    default: () => ({}),
  },
});

//撤销申请
const getCancelApplication = async () => {
  const res = await projectSpaceApi.getuserCanel({ type: 'CreateProject', poolId: store.state.poolInfo?.id });
  if (res.code === 0) {
    message.success('申请撤回成功');
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  } else {
    message.error('申请撤回失败，请稍后再试');
  }
};

const handleOk = (e) => {
  Modal.confirm({
    title: '确定要撤回新建项目空间申请吗?',
    icon: createVNode(ExclamationCircleFilled),
    content: '撤回申请后，已提交的填写内容不保留，您可前往申请新建项目空间页面重新发起申请',
    onOk() {
      getCancelApplication();
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
  formRef.value
    .validate()
    .then(() => {
      open.value = false;
      emits('ok', formState);
    })
    .catch((error) => {
      throw new Error(error);
    });
};
const handleCancel = () => {
  open.value = false;
  emits('cancel');
};
watch(
  () => open.value,
  (state) => {
    if (state) {
      nextTick(() => {
        formRef.value.resetFields();
      });
    }
  }
);
</script>

<style lang="less" scoped>
@import './common.less';
:deep(.ant-form-item) {
  .ant-form-margin-offset {
    margin-bottom: 0 !important;
  }
}
:deep(.ant-form-item) {
  margin-bottom: 5px;
}

//文本color
:deep(.ant-form-item-control) {
  color: #00141a;
}
.ant-modal :deep(.ant-modal-content) {
  padding: 0;
}
</style>
<style>
.test :deep(.ant-modal .ant-modal-content) {
  padding-top: 0px;
}

.test .ant-modal-confirm-btns {
  margin-top: 24px;
}
.test .ant-modal-confirm-body > .anticon {
  margin-bottom: 4px;
}
.test .ant-modal-confirm-body .ant-modal-confirm-title {
  color: #00141a;
  margin-bottom: 4px;
}
.test .ant-modal-content {
  padding-top: 24px;
}
</style>
