<template>
  <a-modal v-model:open="open" class="create-modal" @ok="handleOk" @cancel="handleCancel">
    <template #title>
      <p class="modal-title">申请新建项目空间</p>
    </template>
    <a-form ref="formRef" :model="formState" :rules="rules" :label-col="labelCol">
      <a-form-item ref="reqTitle" label="需求名称" name="reqTitle">
        <a-input v-model:value="formState.reqTitle" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="需求描述" name="reqDesc" class="form-item-no-margin">
        <JtTextarea v-model:value="formState.reqDesc" :rows="4" class="textarea space-info" show-count placeholder="请输入" :maxlength="1000" />
      </a-form-item>
      <a-form-item label="使用资源池" name="poolId">
        <a-select v-model:value="formState.poolId" :disabled="!poolSelectable" placeholder="请选择" :options="poolOptions" style="margin: -5px 0; min-width: 200px"> </a-select>
      </a-form-item>
      <a-form-item label="姓名" name="fullName">
        <a-input v-model:value="formState.fullName" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="工作单位" name="company">
        <a-input v-model:value="formState.company" placeholder="请输入" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import JtTextarea from '@/components/JtTextarea.vue';
import { projectSpaceApi } from '@/apis';

import { useStore } from 'vuex';
import { watchEffect } from 'vue';

const store = useStore();

const emits = defineEmits(['cancel', 'ok']);
const open = defineModel('open', { type: Boolean, default: false });
const formRef = ref();

const labelCol = { style: { width: '90px' } };

const formState = reactive({
  reqTitle: '',
  reqDesc: '',
  poolId: null,
  fullName: '',
  company: '',
  type: 'CreateProject', //工单类型
  reqQuota: { crprj: null }, //申请配额
});

const pools = computed(() => store.state.poolList);

const currentPool = computed(() => store.state.poolInfo);
const poolSelectable = ref(true);
watch(
  () => open.value,
  async (state) => {
    if (state) {
      nextTick(() => {
        formRef.value.resetFields();
      });
      getPoolList();
      if (currentPool.value.id) {
        formState.poolId = +currentPool.value.id;
        poolSelectable.value = false;
      }
    }
  }
);

const rules = {
  reqTitle: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (value.trim() === undefined || value.trim() === null || value.trim() === '') {
          return Promise.reject('请输入');
        }
        if (value.length > 50) {
          return Promise.reject('50字以内');
        }
        return Promise.resolve();
      },
    },
  ],
  reqDesc: [
    { required: true, message: '请输入' },
    { max: 1000, message: '1000字以内', trigger: ['blur', 'change'] },
  ],
  poolId: [{ required: true, message: '请选择' }],
  fullName: [
    // {
    //   required: true,
    //   message: '请输入',
    // },
    // { max: 30, message: '30字以内', trigger: ['blur', 'change'] },
    {
      required: true,
      trigger: ['blur', 'change'],
      validator: (rule, value, callback) => {
        if (value.trim() === undefined || value.trim() === null || value.trim() === '') {
          return Promise.reject('请输入');
        }
        if (value.length > 30) {
          return Promise.reject('30字以内');
        }
        return Promise.resolve();
      },
    },
  ],
  company: [
    {
      required: true,
      trigger: ['blur', 'change'],
      validator: (rule, value, callback) => {
        if (value.trim() === undefined || value.trim() === null || value.trim() === '') {
          return Promise.reject('请输入');
        }
        if (value.length > 50) {
          return Promise.reject('50字以内');
        }
        return Promise.resolve();
      },
    },
  ],
};

const poolOptions = ref([]);
//获取资源池信息
const getPoolList = async () => {
  const res = await projectSpaceApi.getCreatPoolinfo();
  if (res.code === 0) {
    poolOptions.value = res.data.map((item) => ({ label: item.poolName, value: item.poolId }));
  } else {
    message.error('获取资源池信息失败，请稍后再试');
  }
};

// 提交申请
function handleOk() {
  formRef.value
    .validate()
    .then(async () => {
      const res = await projectSpaceApi.userQuotaRequest(formState);
      if (res.code === 0) {
        open.value = false;
        emits('ok', formState);
        message.success('申请提交成功');
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else if (res.code === 130203) {
        message.error('已有在途申请');
      } else {
        message.error('申请提交失败，请稍后再试');
      }
    })
    .catch(() => {
      console.log('表单校验失败');
      // throw new Error(error);
    });
}

// const handleOk = (e) => {
//   formRef.value
//     .validate()
//     .then(() => {
//       open.value = false;
//       console.log(formState, 'formState');
//       // store.commit('UPDATE_APPLY_PERMISSION', formState);
//       emits('ok', formState);
//     })
//     .catch((error) => {
//       throw new Error(error);
//     });
// };
const handleCancel = () => {
  open.value = false;
  emits('cancel');
};
</script>

<style lang="less" scoped>
@import './common.less';
:deep(.ant-form-item) {
  .ant-form-margin-offset {
    margin-bottom: 0 !important;
  }
}
:deep(.ant-form-item) {
  margin-bottom: 32px;
}
</style>
