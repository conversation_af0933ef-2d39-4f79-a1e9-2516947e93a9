<template>
  <a-layout-header class="header">
    <div>
      <a class="logo-container" :href="linkUrl">
        <img v-if="logoUrl" style="width: 126px; height: 26px" :src="logoUrl" alt="" />
      </a>
    </div>
    <div class="right">
      <a-space>
        <a-dropdown>
          <div class="user-box">
            <img class="avatar" :src="userInfo?.image || defaultAvatar" alt="" />
            <p :title="userInfo?.userName" style="margin: 0">{{ userInfo?.userName }}</p>
          </div>
          <template #overlay>
            <a-menu style="display: inline-block">
              <a-menu-item @click="handleLogout"><span style="font-size: 12px; color: #555555">退出登录</span> </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </a-space>
    </div>
  </a-layout-header>
</template>

<script setup>
import { useStore } from 'vuex';
import { logout } from '../keycloak';
import defaultAvatar from '../assets/images/avatar_big.png';
import { useRoute } from 'vue-router';
import iconTop from '@/assets/images/logo.png';

const route = useRoute();
const store = useStore();
const userInfo = computed(() => store.state.userInfo);
const logoUrl = computed(() => store.state.layoutConfig?.iconTop || iconTop);
const linkUrl = computed(() => store.state.layoutConfig.link);

const handleLogout = () => {
  let redirectUrl = location.href;
  if (route.path === '/no-auth') {
    redirectUrl = `${location.origin}${location.pathname}`;
  }
  logout(redirectUrl);
};
</script>

<style lang="less" scoped>
.header {
  position: sticky;
  top: 0;
  width: 100%;
  display: flex;
  background: #0d273e;
  height: 50px;
  padding: 0 32px 0 8px;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1px;
  box-shadow: 0px -1px 8px 1px rgba(0, 0, 0, 0.2);
  z-index: 100;
  color: #fff;
  .trigger {
    font-size: 18px;
  }
  .right {
    height: 50px;
    display: flex;
    align-items: center;
  }
  .center-info {
    cursor: pointer;
  }
  .center-info,
  .msg-center {
    margin-right: 20px;
  }
}
.user-box {
  display: flex;
  align-items: center;
  cursor: pointer;
  .avatar {
    width: 28px;
    height: 28px;
    margin-right: 8px;
    border-radius: 50%;
  }
  p {
    font-size: 12px;
    line-height: 20px;
    max-width: 85px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.top-link {
  font-size: 12px;
  color: #555;
}
.logo-container {
  display: flex;
  width: 200px;
  justify-content: center;
}
</style>
