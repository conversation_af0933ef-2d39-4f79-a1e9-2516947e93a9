<template>
  <div :class="pools.length > 0 ? 'resource' : 'resource select-hidden'">
    <EnvironmentOutlined class="local-icon" />
    <a-select
      ref="select"
      v-model:value="selectedPool"
      :bordered="false"
      :get-popup-container="
        (triggerNode) => {
          return triggerNode.parentNode;
        }
      "
      class="select-pool"
      @change="handleChange"
    >
      <template #suffixIcon><CaretDownOutlined class="ant-select-suffix" /></template>
      <a-select-option v-for="item in pools" :key="item.id" :value="item.id">{{ item.resourcePoolName }}</a-select-option>
    </a-select>
  </div>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { EnvironmentOutlined } from '@ant-design/icons-vue';
import { changePoolAndReload } from '@/utils';
import { CaretDownOutlined } from '@ant-design/icons-vue';

const store = useStore();

const selectedPool = ref(Number(store.state.poolInfo.id));
const pools = computed(() => store.state.poolList);

const handleChange = (value) => {
  const poolItem = pools.value.find((item) => item.id === value);
  // 切换资源池时，刷新并跳转到概览页
  changePoolAndReload(poolItem.id);
};
</script>

<style lang="less" scoped>
.resource {
  display: inline-block;
  margin-left: 24px;
  &.select-hidden {
    visibility: hidden;
  }
}
.local-icon {
  color: rgba(0, 20, 26, 0.7);
}
:deep(.ant-select-dropdown) {
  min-width: 136px !important;
  top: 42px !important;
}
:deep(.ant-select-selector) {
  font-weight: 400;
  font-size: 12px;
  color: rgba(0, 20, 26, 0.7);
}
:deep(.ant-select-selection-item:after) {
  font-weight: 400;
  font-size: 12px;
  color: rgba(0, 20, 26, 0.7);
}
:deep(.ant-select-item-option) {
  padding: 9px 8px;
  font-weight: 400 !important;
}
:deep(.ant-select-item-option-selected) {
  background: #e6f7fa;
}
</style>
