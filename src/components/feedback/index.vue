<template>
  <jt-micro-components module-name="common-feedback" :data="{ requester, config }" />
</template>

<script>
import { GET, POST } from '@/request/requestMicroComponents';
import { getRedirectUrlPrefix } from '@/utils';
import { getEnvConfig } from '@/config';
export default {
  name: 'CommonFeedbackRemote',
  props: {
    size: {
      // 默认用小size, 传large用大的size
      type: String,
      default: 'normal',
    },
    appName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      requester: { GET, POST },
      config: {
        size: this.size,
        showRequirment: getEnvConfig('SHOW_REQUIRMENT_ICON') === '1',
        hostPlatform: 'DMX_KFPT',
        helpcenterUrl: `${getRedirectUrlPrefix()}common-helpcenter#/homepage?platformCode=DMX_KFPT`,
        contactEmail: getEnvConfig('CONTACT_EMAIL'),
      },
    };
  },
};
</script>
