<template>
  <jt-micro-components module-name="common-feedback-modal" :data="{ requester, config, visible }" @cancel="$emit('cancel')" @ok="$emit('ok')" />
</template>

<script>
import { GET, POST } from '@/request/requestMicroComponents';

export default {
  name: 'CommonFeedbackRemote',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      requester: { GET, POST },
    };
  },
  computed: {
    config() {
      return {
        hostPlatform: '大模型开发平台',
      };
    },
  },
};
</script>
