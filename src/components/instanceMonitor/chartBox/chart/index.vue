<template>
  <div class="chart-wrap">
    <v-chart class="chart" :option="option" />
  </div>
</template>

<script>
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent, DataZoomComponent } from 'echarts/components';
import VChart, { THEME_KEY } from 'vue-echarts';
import { ref, defineComponent, onMounted } from 'vue';
import { getDataSeries } from './chart-data.js';

use([<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, PieChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, DataZoomComponent]);

export default defineComponent({
  components: {
    VChart,
  },
  props: {
    chartColorMap: {
      type: Array,
      default: () => [],
    },
    chartData: {
      type: Array,
      default: () => [],
    },
    chartXAxisData: {
      type: Array,
      default: () => [],
    },
    chartTitle: {
      type: String,
      default: '',
    },
    chartAxisLabelFormatter: {
      type: [String, Function],
      default: '',
    },
    chartTooltipNeedTitle: {
      type: Boolean,
      default: false,
    },
    chartDataMax: {
      type: [Number],
    },
  },
  setup: (props) => {
    const option = ref({});
    const getChartData = async () => {
      option.value = {};
      option.value = { ...getDataSeries({ ...props }) };
    };

    watch(props, () => {
      getChartData();
    });
    onMounted(getChartData);
    return {
      option,
    };
  },
});
</script>

<style lang="less" scoped>
.chart-box {
  position: relative;
  span {
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    max-width: 130px;
    height: 24px;
    text-align: center;
  }
}
.chart {
  height: 400px;
}
.storage-content {
  display: flex;
  h2 {
    margin-bottom: 0;
    font-size: 14px;
  }
}
.chart-wrap {
  width: 100%;
}
.storage-table {
  width: 50%;
  flex-shrink: 0;
}
:deep(.tooltip-wrap) {
  min-width: 200px;
  .tooltip-content {
    margin-bottom: 8px;
  }
  .dot-common {
    width: 8px;
    height: 8px;
    background: #568af1;
    border-radius: 100%;
    margin-right: 8px;
    display: block;
  }
  .tooltip-title {
    font-size: 12px;
    color: #121f2c;
    line-height: 17px;
    margin-bottom: 13px;
  }
  .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    p {
      display: flex;
      align-items: center;
      color: rgba(96, 105, 114, 1);
      font-size: 12px;
      line-height: 17px;
      &:nth-of-type(2) {
        color: #121f2c;
        font-size: 14px;
        line-height: 20px;
      }
    }
  }
  .tooltip-footer {
    padding-top: 8px;
    font-size: 12px;
    font-weight: 400;
    color: #606972;
    line-height: 18px;
    border-top: 1px solid #e0e1e1;
  }
}
:deep(.jt-pagination) {
  .left {
    div {
      display: none;
    }
  }
}
</style>
