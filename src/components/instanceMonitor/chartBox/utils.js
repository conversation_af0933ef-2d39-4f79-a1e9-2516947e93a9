// 适配给图表用的颜色
export const colorMap = ['rgba(43, 180, 214, 1)', 'rgba(242, 73, 67, 1)', 'rgba(249, 73, 128, 1)', 'rgba(141, 101, 248, 1)', 'rgba(200, 74, 201, 1)', 'rgba(58, 146, 250, 1)', 'rgba(39, 194, 186, 1)', 'rgba(249, 204, 69, 1)'];

export const echartsCommonOptions = {
  grid: {
    left: '10%',
    right: '10%',
    bottom: 60,
  },
};

/***
 * 乘法，获取精确乘法的结果值
 * @param arg1
 * @param arg2
 * @returns
 */
function accMul(arg1, arg2) {
  let m = 0;
  let s1 = '';
  let s2 = '';
  try {
    s1 = arg1.toString();
    s2 = arg2.toString();
  } catch (e) {
    s1 = '0';
    s2 = '0';
  }
  try {
    m += s1.split('.')[1].length;
  } catch (e) {
    // console.warn(e);
  }
  try {
    m += s2.split('.')[1].length;
  } catch (e) {
    // console.warn(e);
  }
  return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
}
/**
 * 获取对应值的带单位的额度
 * @params data:number|string 需要转换的数字
 * @params toFixedLength:number 保留几位小数，默认值=1
 * @params returnType:number 返回值的类型（0=数字+单位，1=数字，2=单位），默认值=1
 * @returns 转换后的储存量，根据参数(returnType)返回不同的值
 * ps:先将获取到的数据统一转换为字节（b），然后再从字节层面统一向上计算
 */
export function getUsageResult(data, returnType = 0, toFixedLength = 1) {
  const dataSize = accMul(data, accMul(accMul(1024, 1024), 1024));
  let sizeTxt;
  let unit;
  if (dataSize / 1024 < 1) {
    sizeTxt = Math.ceil(dataSize * 100) / 100;
    unit = 'B';
  } else if (dataSize / 1024 / 1024 < 1) {
    sizeTxt = Math.ceil((dataSize / 1024) * 100) / 100;
    unit = 'KB';
  } else if (dataSize / 1024 / 1024 / 1024 < 1) {
    sizeTxt = Math.ceil((dataSize / 1024 / 1024) * 100) / 100;
    unit = 'MB';
  } else if (dataSize / 1024 / 1024 / 1024 / 1024 < 1) {
    sizeTxt = Math.ceil((dataSize / 1024 / 1024 / 1024) * 100) / 100;
    unit = 'GB';
  } else {
    sizeTxt = Math.ceil((dataSize / 1024 / 1024 / 1024 / 1024) * 100) / 100;
    unit = 'TB';
  }
  if (returnType == 1) {
    return sizeTxt.toFixed(toFixedLength);
  }
  if (returnType == 2) {
    return unit;
  }
  return sizeTxt.toFixed(toFixedLength) + unit;
}
