<template>
  <div>
    <chart :chart-data="chartData" :chart-data-max="chartDataMax" :chart-color-map="chartColorMap" :chart-tooltip-need-title="chartTooltipNeedTitle" :chart-axis-label-formatter="chartAxisLabelFormatter" :chart-x-axis-data="chartXAxisData" :chart-title="chartTitle"></chart>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import chart from './chart/index.vue';

export default defineComponent({
  components: { chart },
  props: {
    chartColorMap: {
      type: Array,
      default: () => [],
    },
    chartData: {
      type: Array,
      default: () => [],
    },
    chartXAxisData: {
      type: Array,
      default: () => [],
    },
    chartTitle: {
      type: String,
      default: '',
    },
    chartAxisLabelFormatter: {
      type: [String, Function],
      default: '{value}',
    },
    chartTooltipNeedTitle: {
      type: <PERSON>olean,
      default: false,
    },
    chartDataMax: {
      type: [Number],
    },
  },
  setup() {
    return {};
  },
});
</script>

<style lang="less" scoped></style>
