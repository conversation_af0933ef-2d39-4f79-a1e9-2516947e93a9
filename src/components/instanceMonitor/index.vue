<template>
  <jt-container style="margin-top: -15px">
    <a-flex justify="space-between" align="flex-end" class="content-title">
      <p>实例监控_{{ podName }}</p>
      <slot v-if="$slots.titleExtra" name="titleExtra"></slot>
      <template v-else>
        <a-space class="title-right-extra">
          <a-radio-group v-model:value="type" @change="changeTimeRadio">
            <a-radio-button :value="MONITOR_TIME_MAP.FIVE_MIN">近5分钟</a-radio-button>
            <a-radio-button :value="MONITOR_TIME_MAP.HALF_HOUR">近30分钟</a-radio-button>
            <a-radio-button :value="MONITOR_TIME_MAP.ONE_HOUR">近1小时</a-radio-button>
            <a-radio-button :value="MONITOR_TIME_MAP.ONE_DAY">近1天</a-radio-button>
          </a-radio-group>
          <a-select v-if="props.showInstanceSelect" v-model:value="podName" style="width: 224px" @change="changeInstanceSelect">
            <a-select-option v-for="x in instanceSelectList" :key="x.podName" :value="x.podName">
              <a-tooltip placement="top" :title="x.podName">
                {{ `${x.podName.slice(0, 15)}...${x.podName.slice(x.podName?.length - 6, x.podName?.length)}` }}
              </a-tooltip>
            </a-select-option>
          </a-select>
        </a-space>
      </template>
    </a-flex>
    <jt-container-item style="padding-right: 70px; padding-bottom: 8px">
      <a-flex wrap="wrap" justify="space-between">
        <div v-for="(x, key) in chartDataList" v-show="x.show" :key="key" class="chart-wrap">
          <chart-box :chart-data-max="x.chartDataMax" :chart-tooltip-need-title="x.chartTooltipNeedTitle" :chart-color-map="x.chartColorMap" :chart-axis-label-formatter="x.chartAxisLabelFormatter" class="chart-box" :chart-x-axis-data="x.chartXAxisData" :chart-title="x.chartTitle" :chart-data="x.chartData" />
        </div>
      </a-flex>
    </jt-container-item>
  </jt-container>
</template>

<script setup>
import { useStore } from 'vuex';

import { TASK_TYPE, taskApiInfo, MONITOR_TIME_MAP } from '@/constants/trainTask';
import { requestWithProjectId } from '@/request';
const { GET, POST } = requestWithProjectId;
import { getFormatTime } from '@/utils';

import chartBox from './chartBox/index.vue';

const store = useStore();
const props = defineProps({
  instanceId: {
    type: String,
  },
  taskType: {
    type: String,
    default: TASK_TYPE.DEV,
  },
  title: {
    type: String,
    default: '实例监控',
  },
  showInstanceSelect: {
    type: Boolean,
    default: true,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
  projectId: {
    // 运管侧，由于没有进入具体项目，需传入该值
    type: String,
    default: '',
  },
});
const type = defineModel('type', { default: MONITOR_TIME_MAP.FIVE_MIN });
const podName = defineModel('podName', { default: undefined });

const GET_CHART_DATA_TIME_DELAY = 60000;
const chartDataList = ref({});
const instanceSelectList = ref([]);
const isGpu = computed(() => {
  return taskInfo.data.resourceInfo.gpu;
});

let taskInfo = {};
const getInstanceSelectList = async () => {
  const apis = props.taskType === TASK_TYPE.TASK ? [getDetailInfo(), getInstanceList()] : [getDetailInfo()];
  return Promise.all(apis).then((res) => {
    const [detailInfo, podInfoList = {}] = res;
    if (props.taskType === TASK_TYPE.DEV) {
      taskInfo = { ...detailInfo, ...{ total: 0 } };
    } else {
      taskInfo = { ...detailInfo, ...{ podInfoList: podInfoList?.data?.list || [], total: podInfoList?.data?.total || 0 } };
    }
    instanceSelectList.value = taskInfo.data.podInfoList ?? taskInfo.podInfoList;
  });
};
const getDetailInfo = async () => {
  if (props.isAdmin) {
    return GET(taskApiInfo(props.taskType).adminDetail, {
      id: props.instanceId,
      projectId: props.projectId,
    });
  } else {
    return GET(taskApiInfo(props.taskType).detail, {
      id: props.instanceId,
    });
  }
};

const getInstanceList = async () => {
  if (props.isAdmin) {
    return POST(taskApiInfo(props.taskType).getAdminPodList, {
      id: props.instanceId,
      projectId: props.projectId,
      pageNum: 1,
      pageSize: 999999999,
    });
  } else {
    return POST(taskApiInfo(props.taskType).getPodList, {
      id: props.instanceId,
      pageNum: 1,
      pageSize: 999999999,
    });
  }
};

let getChartDataTimer = null;
const getChartData = (needLoading = true) => {
  clearTimeout(getChartDataTimer);
  const params = {
    dimension: type.value,
    podName: podName.value,
  };
  if (props.isAdmin) {
    params.projectId = props.projectId;
  }
  if (needLoading) {
    store.dispatch('updateGlobalLoading', true);
  }
  const url = props.isAdmin ? taskApiInfo(props.taskType).getAdminMonitorData : taskApiInfo(props.taskType).getMonitorData;
  POST(url, params)
    .then((res) => {
      if (res.code === 0) {
        const dataObj = { ...res.data };
        for (const i in dataObj) {
          if (dataObj[i] === null || dataObj[i] === undefined) {
            dataObj[i] = [];
          }
        }
        formatChartsDataObj(dataObj);
      }
    })
    .finally(() => {
      if (needLoading) {
        store.dispatch('updateGlobalLoading', false);
      }
      getChartDataTimer = setTimeout(() => {
        getChartData(false);
      }, GET_CHART_DATA_TIME_DELAY);
    });
};

const formatChartsDataObj = (data) => {
  chartDataList.value = {
    gpuUsageRate: {
      chartTitle: '{title|加速卡使用率}',
      chartData: multiChartsDataFormat(data.gpuUsageRate),
      chartXAxisData: formatChartsDataItem(data.gpuUsageRate[Object.keys(data.gpuUsageRate)[0]])?.xAxis,
      chartAxisLabelFormatter: (value) => (value ? value + '%' : value),
      chartTooltipNeedTitle: true,
      chartDataMax: 100,
      show: isGpu.value,
    },
    gpuMemUsageRate: {
      chartTitle: '{title|加速卡显存使用率}',
      chartData: multiChartsDataFormat(data.gpuMemUsageRate),
      chartXAxisData: formatChartsDataItem(data.gpuMemUsageRate[Object.keys(data.gpuMemUsageRate)[0]])?.xAxis,
      chartAxisLabelFormatter: (value) => (value ? value + '%' : value),
      chartTooltipNeedTitle: true,
      chartDataMax: 100,
      show: isGpu.value,
    },
    gpuMemUsage: {
      chartTitle: '{title|加速卡显存使用量}{subTitle|（GB）}',
      chartData: multiChartsDataFormat(data.gpuMemUsage),
      chartXAxisData: formatChartsDataItem(data.gpuMemUsage[Object.keys(data.gpuMemUsage)[0]])?.xAxis,
      chartTooltipNeedTitle: true,
      show: isGpu.value,
    },
    memUsageRate: {
      chartTitle: '{title|内存使用率}',
      chartData: [{ name: '内存使用率', data: formatChartsDataItem(data.memUsageRate).data }],
      chartXAxisData: formatChartsDataItem(data.memUsageRate).xAxis,
      chartAxisLabelFormatter: (value) => (value ? value + '%' : value),
      chartDataMax: 100,
      show: true,
    },
    memUsage: {
      chartTitle: '{title|内存使用量}{subTitle|（GB）}',
      chartData: [{ name: '内存使用量', data: formatChartsDataItem(data.memUsage).data }],
      chartXAxisData: formatChartsDataItem(data.memUsage).xAxis,
      show: true,
    },
    cpuUsageRate: {
      chartTitle: '{title|CPU使用率}',
      chartData: [{ name: 'CPU使用率', data: formatChartsDataItem(data.cpuUsageRate).data }],
      chartXAxisData: formatChartsDataItem(data.cpuUsageRate).xAxis,
      chartAxisLabelFormatter: (value) => (value ? value + '%' : value),
      chartDataMax: 100,
      show: true,
    },
  };
};
const formatChartsDataItem = (data) => {
  const formatData = {
    data: [],
    xAxis: [],
  };
  if (!data) {
    return formatData;
  }
  data.map((x) => {
    formatData.data.push(x.usage || x.value || []);
    formatData.xAxis.push(getFormatTime(x.timestamp * 1000, 'YYYY-MM-DD HH:mm'));
  });
  return formatData;
};
const multiChartsDataFormat = (data) => {
  const formatData = [];
  // 按照数字顺序排序
  const keys = Object.keys(data).sort((a, b) => {
    // 使用正则表达式提取键名中的数字部分
    const numA = parseInt(a.match(/\d+$/)[0], 10);
    const numB = parseInt(b.match(/\d+$/)[0], 10);
    return numA - numB; // 升序排序
  });
  for (const i of keys) {
    const _data = [];
    data[i].map((x) => {
      _data.push(x.usage || x.value || []);
    });
    formatData.push({
      name: i,
      data: _data,
    });
  }
  return formatData;
};

const changeInstanceSelect = (value) => {
  podName.value = value;
  getChartData();
};
const changeTimeRadio = (e) => {
  getChartData();
};
onMounted(() => {
  if (podName.value) {
    if (props.showInstanceSelect) {
      getInstanceSelectList().then(() => {
        getChartData();
      });
    } else {
      getChartData();
    }
  }
});
onUnmounted(() => {
  clearTimeout(getChartDataTimer);
});
</script>

<style lang="less" scoped>
.chart-wrap {
  width: 50%;
}
.chart-box {
  width: 100%;
}
</style>
