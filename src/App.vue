<template>
  <div id="app">
    <preview-mask v-if="preview" />
    <a-config-provider :locale="zhCN" :theme="theme" :get-popup-container="getPopupContainer">
      <jt-global-loading>
        <layout v-if="!isFullScreenRoute && accessEnabled && poolAccessEnabled">
          <router-view :key="routeViewRefreshKey" />
        </layout>
        <template v-else>
          <router-view :key="routeViewRefreshKey" />
        </template>
        <!-- <jt-feedback v-if="showFeedback" /> -->
        <jt-feedback v-if="false" />
        <jt-system-announcement v-model:visible="showSystemAnnouncement" v-bind="systemAnnouncementContent" />
      </jt-global-loading>
    </a-config-provider>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import layout from '@/components/layout.vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import theme from '@/assets/styles/theme';
import { POPUP_PREVIEW_STORAGE_KEY } from '@/constants/announcement.js';
import { getItem, setItem, removeItem } from '@/utils/storageUtil';
import { changeQueryBeforeHash, isEntranceBySasac, replaceCurrentUrl, updateUrlAndRefresh } from '@/utils/index';
import { PROJECT_KEY, POOLID_KEY, SYS_POPUP_UUID, SYS_POPUP_CLOSE_UUID } from '@/constants/index';
import { getAccessEnabled, getAccessProjects, getAccessPools, checkPermissionByApvAndMgtItem } from '@/keycloak';
import JtFeedback from '@/components/feedback';
import JtFeedbackModal from '@/components/feedback-modal/index.vue';
import JtSystemAnnouncement from '@/components/system-announcement.vue';
import previewMask from '@/components/previewMask.vue';
import { getResourcePoolInfo as getResourcePoolInfoApi } from '@/apis/resource';
import { changePoolAndReload } from '@/utils';
import GlobalLoding from './components/GlobalLoding.vue';
import { noticeApi } from '@/apis';
import { USR_NOTICE_EDIT_AUTH } from '@/constants/announcement';

export default {
  name: 'App',
  components: {
    layout,
    JtFeedback,
    JtGlobalLoading: GlobalLoding,
    JtSystemAnnouncement,
    previewMask,
  },
  data() {
    return {
      zhCN,
      theme,
      accessEnabled: getAccessEnabled(),
      isFullScreenRoute: true,
      poolAccessEnabled: getAccessPools().length > 0, // 是否存在资源池的访问权限
      showFeedback: false,

      systemAnnouncementContent: { title: '', content: '', uuid: undefined },
      showSystemAnnouncement: false,
      sysAnnoTimer: null,
      preview: false,
    };
  },
  computed: {
    ...mapState(['globalLoading', 'globalLoadingText', 'routeViewRefreshKey', 'jtFeedbackModalVisible', 'poolListLoading', 'menuDataLoaded', 'poolInfo']),
    upgradStateAbleEnterPage: () => {
      return checkPermissionByApvAndMgtItem(USR_NOTICE_EDIT_AUTH);
    },
  },

  watch: {
    $route(to, from) {
      this.isFullScreenRoute = to.meta.fullscreen;
      // 当访问运营管理平台时poolAccessEnabled默认为true
      if (this.$store.state.menuHasPlatform) {
        this.poolAccessEnabled = true;
      }
      // 国资-sasac，不展示问题反馈
      // 大屏不展示问题反馈
      this.showFeedback = !to.meta.largeScreen && !isEntranceBySasac();
      // 系统公告弹窗预览
      if (to.path === '/home' && !isEntranceBySasac()) {
        if (+to.query.preview === 1) {
          this.preview = true;
          this.showSystemAnnouncement = true;
          this.systemAnnouncementContent = {
            title: getItem(POPUP_PREVIEW_STORAGE_KEY)?.title,
            content: getItem(POPUP_PREVIEW_STORAGE_KEY)?.content,
          };
        }
      }
    },
    menuDataLoaded() {
      this.getSystemMaintenanceStatus();
    },
  },
  beforeCreate() {
    // 不要在这里面进行实例属性赋值，会不生效
    const accessEnabled = getAccessEnabled();
    if (!accessEnabled) {
      return;
    }
  },
  async created() {
    console.log('created');
    document.getElementById('jt-app-loading')?.remove();
    this.$store.dispatch('getUserInfo');
    // 初始化国资信息
    if (isEntranceBySasac()) {
      this.$store.commit('UPDATE_LAYOUT_CONFIG', {
        iconTop: '',
        iconTopFold: '',
        link: '/sasac-aiopen/#/',
        subTab: '大模型开发平台',
      });
    }
  },
  methods: {
    getPopupContainer(el, dialogContext) {
      if (dialogContext) {
        return dialogContext.getDialogWrap();
      } else {
        return document.body;
      }
    },
    getSystemMaintenanceStatus() {
      // 当访问运营管理平台时不触发当前接口
      if (this.$store.state.menuHasPlatform) return;
      this.$store.dispatch('getSystemMaintenanceStatus').then(() => {
        if (this.$route.meta.showSystemAnno === false) {
          return;
        }
        const res = this.$store.state.systemMaintenanceStatus;
        if (res.code === 0 && res.data) {
          if (!this.upgradStateAbleEnterPage) {
            this.$router.push({ path: '/upgrade' });
          }
        }
        // 预览页面不显示弹窗
        const homePreview = ['/home'].includes(this.$route.path) && this.$route.query.preview && +this.$route.query.preview === 1;
        const upgradePath = this.$route.path === '/upgrade';
        // if (!(this.$route.path === '/upgrade' && this.$route.query.preview && +this.$route.query.preview === 1)) {
        // 国资下不加载系统公告
        if (!upgradePath && !homePreview && !isEntranceBySasac()) {
          this.getSystemMaintenancePopupMessage();
        }
        this.sysAnnoTimer = setTimeout(() => {
          this.getSystemMaintenanceStatus();
        }, 1000 * 60);
      });
    },
    getSystemMaintenancePopupMessage() {
      noticeApi.popupMessage().then((res) => {
        if (res.code === 0 && res.data) {
          this.systemAnnouncementContent = res.data;
          const poolId = this.poolInfo.id;
          if (res.data.uuid !== getItem(SYS_POPUP_UUID(poolId)) && res.data.uuid !== sessionStorage.getItem(SYS_POPUP_CLOSE_UUID(poolId))) {
            this.showSystemAnnouncement = true;
          }
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
#app {
  min-width: 1440px;
  color: #555555;
  background: @jt-background-color;
}
#spin-image {
  width: 412px;
  height: 412px;
  top: 80px;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}
</style>
