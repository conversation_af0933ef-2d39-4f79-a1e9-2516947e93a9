import { requestWithProjectId, requestWithPoolId } from '@/request';
const { GET } = requestWithProjectId;

// 根据存储类型获取配额
export const getQuotaByType = (data) => GET('/web/storage/v1/quota', data);

// 获取minio的endpoint等信息
export const getCredentials = (data) => GET('/web/storage/v1/credentials', data);

//获取对象存储认证信息
export const getcredentials = (data) => GET('web/storage/v1/credentials', data);
export const getAdminCrefentials = (data) => requestWithPoolId.GET('web/admin/storage/v1/credentials', data);
//获取业务域文件路径接口地址
export const getFileServiceUrl = (data) => GET('/web/storage/v1/fileServiceUrl', data);
// 获取对象存储存储桶列表
export const getBucketList = (data) => GET('/web/storage/v1/bucketList', data);
