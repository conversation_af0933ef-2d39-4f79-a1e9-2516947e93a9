import { GET, requestWithPoolId } from '@/request';
const { GET: requestWithPoolIdGet } = requestWithPoolId;

// 第一版接口信息
// 大屏顶部信息
export const getTopInfo = (data) => GET('/web/admin/display/v1/dashboard/portal/top-index', data);
// 各资源池加速卡信息
export const getPoolMessage = (data) => GET('/web/admin/display/v1/dashboard/portal/pool-message', data);
// 厂商加速卡规模占比
export const manufacturerProp = (data) => GET('/web/admin/display/v1/dashboard/portal/manufacturer-prop', data);
// AI加速卡占有率折线图
export const cardRateGraph = (data) => GET('/web/admin/display/v1/dashboard/portal/card-rate-graph', data);

// 第二版接口信息
// AI加速卡占有率折线图
export const cardRateDetail = (data) => requestWithPoolIdGet('/web/admin/display/v1/dashboard/detail/card-rate-graph', data);
// 训练任务
export const topTask = (data) => requestWithPoolIdGet('/web/admin/display/v1/dashboard/detail/top-task', data);
// 获取顶部指标
export const topIndex = (data) => requestWithPoolIdGet('/web/admin/display/v1/dashboard/detail/top-index', data);
// 获取节点列表
export const getNode = (data) => requestWithPoolIdGet('/web/admin/display/v1/dashboard/detail/node', data);
// 获取集群列表
export const getCluster = (data) => requestWithPoolIdGet('/web/admin/display/v1/dashboard/detail/cluster', data);
