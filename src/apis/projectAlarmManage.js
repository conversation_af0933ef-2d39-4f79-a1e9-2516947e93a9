import { requestWithProjectId } from '@/request';
import { getServiceList } from './service';
const { GET: requestWithProjectIdGET, POST: requestWithProjectIdPOST } = requestWithProjectId;

//告警规则
//告警规则列表-分页查询
export const getAlarmRuleList = (data) => requestWithProjectIdPOST('/web/alert/v1/ruler/list', data);

//推理服务列表-分页查询(告警规则用)
export const getServiceListRule = (data) => getServiceList(data);

//根据ids批量获取推理服务列表(告警规则用)
export const getServiceListRuleByIds = (data) => requestWithProjectIdPOST('/web/serving/v1/instance/simple-detail', data);

//训练任务列表-分页查询(告警规则用)
export const getTrainingTaskList = (data) => requestWithProjectIdPOST('/web/task/v1/list', data);

//根据ids批量查询训练任务列表(告警规则用)
export const getTrainingTaskListByIds = (data) => requestWithProjectIdPOST('/web/task/v1/list-by-ids', data);

//获取用户下的所有资源组简要信息
export const getResourceGroups = (data) => requestWithProjectIdPOST('/web/resource/v1/user/list-all-resource-group', data);

//新建告警规则
export const addAlarmRule = (data) => requestWithProjectIdPOST('/web/alert/v1/ruler/create', data);

//编辑告警规则
export const editAlarmRule = (data) => requestWithProjectIdPOST('/web/alert/v1/ruler/edit', data);

//获取告警规则详情
export const getAlarmRuleById = (data) => requestWithProjectIdGET('/web/alert/v1/ruler/detail', data);

//删除告警规则
export const deleteAlarmRuleById = (data) => requestWithProjectIdGET('/web/alert/v1/ruler/delete', data);

//启用/停用告警规则
export const updateAlarmStatus = (data) => requestWithProjectIdGET('/web/alert/v1/ruler/update-status', data);

//校验告警规则是否合法（含校验规则和重复性）
export const checkAlarmRuleName = (data) => requestWithProjectIdGET('/web/alert/v1/ruler/check-ruler-name', data);

//获取告警规则详情下监控对象信息
export const getAlarmObjectByRule = (data) => requestWithProjectIdGET('/web/alert/v1/ruler/alert-object-detail', data);

//告警记录
//告警记录列表-分页查询
export const getAlarmRecordList = (data) => requestWithProjectIdPOST('/web/alert/v1/record/list', data);

//获取告警记录详情
export const getAlarmRecordById = (data) => requestWithProjectIdGET('/web/alert/v1/record/detail', data);

//获取告警记录详情下通知对象信息
export const getNoticeObjectByRecord = (data) => requestWithProjectIdGET('/web/alert/v1/record/object-notice-detail', data);

//获取告警规则配置信息- (公用接口)
export const getAlarmyRuleSetting = (data) => requestWithProjectIdGET('/web/alert/v1/ruler/settings', data);
