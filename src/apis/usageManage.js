import { GET, POST, requestWithPoolId } from '@/request';
const { GET: requestWithPoolIdGet, POST: requestWithPoolIdPost, requestBlob: requestWithPoolIdRequestBlob } = requestWithPoolId;

// 运管侧-用量管理接口
// 引导页总量显示
export const getUsageSummary = (data) => requestWithPoolIdGet('/web/admin/metering/v1/pool/usage-summary', data);
// 用量管理-时间维度
export const getTimeUsageList = (data) => requestWithPoolIdGet('/web/admin/metering/v1/time/usage-list', data);
// 用量管理-项目维度总量
export const getProjectUsageTotal = (data) => requestWithPoolIdPost('/web/admin/metering/v1/project/usage-total', data);
// 用量管理-项目维度列表
export const getProjectUsageList = (data) => requestWithPoolIdPost('/web/admin/metering/v1/project/usage-list', data);
// 用量管理-项目活动维度总量
export const getPrjactUsageTotal = (data) => requestWithPoolIdPost('/web/admin/metering/v1/project-act/usage-total', data);
// 用量管理-项目活动维度列表
export const getPrjactUsageList = (data) => requestWithPoolIdPost('/web/admin/metering/v1/project-act/usage-list', data);
// 用量管理-项目活动维度详情基本信息
export const getPrjactDetail = (data) => requestWithPoolIdGet('/web/admin/metering/v1/project-act/basic-info', data);
// 用量管理-项目活动维度详情列表
export const getPrjactDetailList = (data) => requestWithPoolIdPost('/web/admin/metering/v1/project-act/detail', data);
// 用量管理-项目活动维度详情列表总量
export const getPrjactDetailTotal = (data) => requestWithPoolIdGet('/web/admin/metering/v1/project-act/usage', data);
// 用量管理-获取全部活动类型
export const getPrjactTypes = (data) => requestWithPoolIdGet('/web/admin/metering/v1/project-act/types', data);
// 用量管理-获取项目空间
export const getProjectSpaceAllList = (data) => requestWithPoolIdGet('/web/admin/project/v1/project/list-all', data);

// 用量管理-时间维度列表-导出
export const getTimeExport = (data) => requestWithPoolIdRequestBlob('/web/admin/metering/v1/time/usage-export', data, { responseType: 'blob' });
// 用量管理-项目维度-导出
export const getProjectExport = (data) => requestWithPoolIdRequestBlob('/web/admin/metering/v1/project/usage-export', data, { responseType: 'blob' });
// 用量管理-项目活动维度详情-导出
export const getProjectActExport = (data) => requestWithPoolIdGet('/web/admin/metering/v1/project-act/usage-export', data);
// 用量管理-项目活动维度详情-明细导出
export const getDetailExport = (data) => requestWithPoolIdGet('/web/admin/metering/v1/project-act/detail-export', data);
