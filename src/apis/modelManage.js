import { requestWithProjectId } from '@/request/index';
const { POST, GET } = requestWithProjectId;
// 获取模型列表
export const getModelList = (data) => POST('/web/model/v1/manage/list', data);
// 获取预置模型列表
export const getPresetModelList = (data) => POST('/web/model/manage/preset/v1/page-list', data);
// 检查模型名称是否重复
export const judgeNameExist = (data) => POST('/web/model/v1/manage/name-check', data, { 'Content-Type': 'multipart/form-data' });

// 获取模型分类，模型来源
export const getModelType = (data) => GET('/web/model/v1/manage/dict', data);

// 获取模型框架，网络，网络版本
export const getModelNetworkMap = (data) => GET('/web/model/v1/manage/frameworks', data);

// 新建模型
export const createModel = (data) => POST('/web/model/v1/manage/create', data, { 'Content-Type': 'multipart/form-data' });

// 获取模型下的所有版本
export const getAllVersion = (data) => GET('/web/model/v1/manage/version/all', data);

// 编辑模型
export const editModel = (data) => POST('/web/model/v1/manage/edit', data, { 'Content-Type': 'multipart/form-data' });

// 删除模型
export const deleteModel = (data) => GET('/web/model/v1/manage/delete', data);

// 获取模型能否被删除
export const canDeleteModel = (data) => GET('/web/model/v1/manage/delete-check', data);

// 获取模型详情
export const getModelDetail = (data) => GET('/web/model/v1/manage/detail', data);

//查询预置模型详情
export const getPresetModelDetail = (data) => GET('/web/model/manage/preset/v1/detail', data);

// 获取版本列表
export const getVersionList = (data) => POST('/web/model/v1/manage/version/list', data);

// 编辑版本
export const editVersion = (data) => POST('/web/model/v1/manage/version/edit', data);

// 删除版本
export const deleteVersion = (data) => POST('/web/model/v1/manage/version/delete', data, { 'Content-Type': 'multipart/form-data' });

// 获取版本能否被删除
export const canDeleteVersion = (data) => GET('/web/model/v1/manage/version-delete-check', data);

// 版本号生成
export const getVersionGenerate = (data) => POST('/web/model/v1/manage/version/num/generate', data, { 'Content-Type': 'multipart/form-data' });

// 新增版本
export const submitNewVersion = (data) => POST('/web/model/v1/manage/version/add', data, { 'Content-Type': 'multipart/form-data' });

// 正在上传列表
export const getUploadingList = (data) => POST('/web/model/v1/manage/uploadingList', data);

// 文件重试
export const retryUpload = (data) => POST('/web/model/v1/manage/retryUpload', data);

// 上传列表删除
export const deleteUpload = (data) => POST('/web/model/v1/manage/deleteUpload', data);

// 获取模型网络列表
export const getNetworkList = (data) => GET('/web/model/v1/manage/network/list', data);
// 删除网络列表
export const delNetwork = (data) => POST('/web/model/v1/manage/network/delete', data, { 'Content-Type': 'multipart/form-data' });
// 新增模型网络列表
export const addNetwork = (data) => POST('/web/model/v1/manage/network/add', data, { 'Content-Type': 'multipart/form-data' });
