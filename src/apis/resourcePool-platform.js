import { POST, GET } from '@/request';

//运营平台-资源池管理
//获取资源池管理分页列表
export const getResourcePoolList = (data) => POST('/web/admin/resource/v1/platform/get-resource-pool-list', data);

// 获取平台支持的加速卡类型
export const getGPUCardsByPoolId = (data) => GET('/web/admin/resource/v1/platform/get-platform-gpu-card-type', data);

//获取功能信息（基础配置和模块配置）
export const getModuleInfoByPoolId = (data) => GET('/web/admin/resource/v1/platform/get-pool-module-info', data);

// 获取资源池详情
export const getDetailByPoolId = (data) => GET('/web/admin/resource/v1/platform/get-resource-pool-detail', data);

// 更新资源池详情
export const updateResourcePool = (data) => POST('/web/admin/resource/v1/platform/update-resource-pool', data);

// 根据资源池id获取集群信息
export const getClusterByPoolId = (data) => GET('/web/admin/resource/v1/platform/get-pool-cluster-info', data);

// 根据资源池id获取文件资源配额情况
export const getResourceUsageByPoolId = (data) => GET('/web/admin/storage/v1/getResourcePoolUsage', data);

// 根据资源池id获取镜像资源配额情况
export const getResourceQuotasByPoolId = (data) => {
  let url = '/web/admin/image/v1/operator/manage/resource_pool/get_resource_quotas';
  let str = '';
  if (data && Object.keys(data)) {
    Object.keys(data).forEach((key) => {
      str = `${key}=${data[key]}`;
    });
    if (str) {
      url = `${url}?${str}`;
    }
  }
  return POST(url, {});
};

// 根据资源池id获取资源信息分页列表
export const getResourceInfoList = (data) => POST('/web/admin/resource/v1/platform/get-resource-info-list', data);

// 资源池管理新增资源类型
export const addResourceType = (data) => POST('/web/admin/resource/v1/platform/add-resource-type', data);

// 资源池管理删除资源类型
export const deleteResourceType = (data) => POST('/web/admin/resource/v1/platform/delete-resource-type', data);

// 资源池管理编辑功能信息
export const updatePoolModuleInfo = (data) => POST('/web/admin/resource/v1/platform/update-pool-module-info', data);
