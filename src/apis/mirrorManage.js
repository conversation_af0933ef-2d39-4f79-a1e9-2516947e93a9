import { requestWithProjectId } from '@/request/index';
const { POST, GET } = requestWithProjectId;

// 获取预置镜像列表
export const getPrepareMirrorList = (data) => POST('/web/image/v1/preset_image/list_by_page', data);

// 获取自定义镜像列表
export const getCustomMirrorList = (data) => POST('/web/image/v1/image_group/list', data);

// 镜像组名称查重
export const judgeGroupExist = (data) => GET('/web/image/v1/image_group/exist', data);

// 新建镜像组
export const createGroup = (data) => POST('/web/image/v1/image_group/create', data);

// 更新镜像组
export const updateGroup = (data) => POST('/web/image/v1/image_group/update', data);

// 删除镜像组
export const deleteGroup = (data) => POST('/web/image/v1/image_group/delete', data, { 'Content-Type': 'multipart/form-data' });

// 镜像组能否删除
export const canDeleteGroup = (data) => GET('/web/image/v1/image_group/can_delete', data);

// 镜像组能否编辑
export const canEditGroup = (data) => GET('/web/image/v1/image_group/can_modify', data);

// 获取镜像组详情
export const getGroupInfo = (data) => POST('/web/image/v1/image_group/group_info', data, { 'Content-Type': 'multipart/form-data' });

// 获取镜像详情
export const getMirrorDetail = (data) => GET('/web/image/v1/custom_image/artifact', data);

// 获取镜像列表
export const getMirrorList = (data) => POST('/web/image/v1/custom_image/artifacts', data);

// 更新镜像
export const updateMirror = (data) => POST('/web/image/v1/custom_image/update', data);

// 删除镜像
export const deleteMirror = (data) => POST('/web/image/v1/custom_image/delete', data, { 'Content-Type': 'multipart/form-data' });

// 镜像能否删除
export const canDeleteMirror = (data) => POST('/web/image/v1/custom_image/can_delete', data, { 'Content-Type': 'multipart/form-data' });

// 获取命令行上传数据
export const getCommandData = (data) => GET('/web/image/v1/image_management/user_info', data);

// 生成自定义镜像版本
export const getVersion = (data) => GET('/web/image/v1/dev_env/manage/generate_version_num', data);

// 获取剩余镜像资源
export const getMirrorRemaind = () => GET('/web/image/v1/dev_env/manage/remaining_capacity');

// 获取对象存储目录下的文件列表
export const getObjectList = (data) => GET('/web/storage/v1/fileList', data);

// 镜像上传
export const uploadMirror = (data) => POST('/web/image/v1/custom_image/upload', data);

// 获取预置镜像详情
export const getPresetImageInfo = (data) => POST('/web/image/v1/preset_image/preset_image_info', data, { 'Content-Type': 'multipart/form-data' });

// 分享镜像
export const shareImage = (data) => POST('/web/image/v1/custom_image/share_image', data);

// 分享镜像列表
export const getShareImageList = (data) => POST('/web/image/v1/custom_image/share_image_list', data);

// 被分享镜像列表
export const getSharedImageList = (data) => POST('/web/image/v1/custom_image/shared_image_list', data);

// 取消分享镜像
export const cancelShareImage = (data) => POST('/web/image/v1/custom_image/share_image_delete', data);

// 同步镜像
export const syncImage = (data) => POST('/web/image/v1/custom_image/sync_image', data);

// 导出镜像
export const exportImage = (data) => POST('/web/image/v1/custom_image/export_image', data);

// 镜像导出记录列表
export const imageExportRecord = (data) => POST('/web/image/v1/custom_image/image_export_record', data);
