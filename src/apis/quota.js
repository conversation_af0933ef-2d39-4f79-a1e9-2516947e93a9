import { GET, POST, requestWithPoolId } from '@/request';
const { GET: requestWithPoolIdGet, POST: requestWithPoolIdPost } = requestWithPoolId;
import { wait } from '@/utils/wait';

const json = {
  code: 0,
  msg: 'OK',
  data: {
    data: [],
    total: 0,
  },
  traceId: 'a99409ee-541d-4216-bfb7-967edf5cc3e9',
};

/**
 * @description: 扩容申请列表
 * @param {*} data
 * @return {*}
 */
export const getExpansionListApi = (data) => requestWithPoolIdPost('/web/admin/approval/v1/project/list', data);

/**
 * @description: 获取工单详情
 * @param {*} data
 * @return {*}
 */
export const getExpansionDetailApi = (data) => requestWithPoolIdGet('/web/admin/approval/v1/project/detail', data);

/**
 * @description: 获取配额列表
 * @param {*} data
 * @return {*}
 *
 */
export const getQuotaListApi = (data) => requestWithPoolIdPost('/web/admin/project/v1/quota/list', data);

/**
 * @description: 获取项目配额详情
 * @param {*} data
 * @return {*}
 */
export const getQuotaDetailApi = (data) => requestWithPoolIdGet('/web/admin/project/v1/quota/detail', data);

/**
 * @description: 项目配额扩容
 * @param {*} data
 * @return {*}
 */
export const scaleQuotaApi = (data) => requestWithPoolIdPost('/web/admin/project/v1/quota/scale', data);

/**
 * @description: 配额变更记录
 * @param {*} data
 * @return {*}
 */
export const getQuotaLogListApi = (data) => requestWithPoolIdGet('/web/admin/project/v1/quota/change/list', data);

/**
 * @description: 工单驳回
 * @param {*} data
 * @return {*}
 */
export const rejectApprovalApi = (data) => requestWithPoolIdPost('/web/admin/approval/v1/project/reject', data);

/**
 * @description: 工单通过
 * @param {*} data
 * @return {*}
 */
export const agreeApprovalApi = (data) => requestWithPoolIdPost('/web/admin/approval/v1/project/agree', data);

/**
 * @description: 获取项目配额变更处理详情
 * @param {*} data
 * @return {*}
 */
export const getProcessDetailApi = async (data) => requestWithPoolIdGet('/web/admin/project/v1/quota/change/process-detail', data);

/**
 * @description: 获取手机号或邮箱
 * @param {*} data
 * @return {*}
 */
export const getProcessEmailPhoneApi = async (data) => requestWithPoolIdGet('/web/admin/approval/v1/project/userInfo', data);

/**
 * @description: 获取当前项目配额使用情况
 * @param {*} data
 * @return {*}
 */
export const getQuotaStatusCardApi = async (data) => requestWithPoolIdGet('/web/admin/project/v1/quota/status', data);

/**
 * @description: 获取项目负责人手机号或邮箱
 * @param {*} data
 * @return {*}
 */
export const getLeaderEmailPhoneApi = async (data) => requestWithPoolIdGet('/web/admin/project/v1/project/leader', data);

/**
 * @description: 获取项目配额变更处理详情
 * @param {*} data
 * @return {*}
 */
export const getQuotaChangeDetailApi = async (data) => requestWithPoolIdGet('/web/admin/project/v1/quota/change/detail', data);

/**
 * @description: 查询配额调整通知结果
 * @param {*} data
 * @return {*}
 */
export const getQuotaChangeNotifyApi = async (data) => requestWithPoolIdGet('/web/admin/project/v1/notify/quota-change', data);

/**
 * @description: 查询审批工单通知结果
 * @param {*} data
 * @return {*}
 */
export const getApprovalNotifyApi = async (data) => requestWithPoolIdGet('/web/admin/project/v1/notify/approval', data);

/**
 * @description: 查看资产使用情况
 * @param {*} data
 * @return {*}
 */
export const getResourceStatusApi = async (data) => requestWithPoolIdGet('/web/admin/project/v1/resource/status', data);
/**
 * @description: 项目配额变更重新发送通知
 * @param {*} data
 * @return {*}
 */
export const renotifyApi = async (data) => requestWithPoolIdGet('/web/admin/project/v1/quota/change/renotify', data);

// 获取用户新建项目审批列表
export const getCreateProjectApprovalList = (data) => requestWithPoolIdPost('/web/admin/approval/v1/user/list', data);

// 获取用户新建项目审批详情
// export const getCreateProjectApprovalDetail = (data) => requestWithPoolIdGet('/web/admin/approval/v1/user/detail', data);
// 获取用户新建项目审批详情(v201)
export const getCreateProjectApprovalDetail = (data) => requestWithPoolIdPost('/web/admin/approval/v1/user/detail', data);

// 获取用户新建项目的列表
export const getCreateProjectListByUser = (data) => requestWithPoolIdPost('/web/admin/project/v1/project/list-user-created-projects', data);

// 获取用户配额变更记录
export const getQuotaChangeList = (data) => requestWithPoolIdPost('/web/admin/project/v1/quota/user/quota-change-list', data);

// 获取用户配额处理详情
export const getQuotaChangeDetail = (data) => requestWithPoolIdGet('/web/admin/project/v1/quota/user/change-detail', data);

// 重新发送用户配额变更通知
export const reNotifyQuotaChange = (data) => requestWithPoolIdGet('/web/admin/project/v1/quota/user/renotify', data);

// 获取用户新建项目配额管理列表
export const getCreateProjectQuotaList = (data) => requestWithPoolIdPost('/web/admin/project/v1/quota/user/project-create-quota-list', data);

// 获取用户新建项目配额详情
export const getCreateProjectQuotaDetail = (data) => requestWithPoolIdGet('/web/admin/project/v1/quota/user/project-create-quota-detail', data);

//用户新建项目配额详情页-调整配额
export const adjustQuota = (data) => requestWithPoolIdPost('/web/admin/project/v1/quota/user/adjust', data);

//新增用户配额
export const addQuota = (data) => requestWithPoolIdPost('/web/admin/project/v1/quota/user/add', data);

// 同意申请
export const agreeQuota = (data) => requestWithPoolIdPost('/web/admin/approval/v1/user/agree', data);

// 驳回申请
export const rejectQuota = (data) => requestWithPoolIdPost('/web/admin/approval/v1/user/reject', data);

//查询用户配额调整通知结果
export const getUserQuotaChange = (data) => requestWithPoolIdGet('/web/admin/project/v1/notify/user-quota-change', data);

// 查询用户审批工单通知结果
export const getUserApprovalResult = (data) => requestWithPoolIdGet('/web/admin/project/v1/notify/user-approval', data);

// 配置查询，查询项目配额最大值
export const getProjectQuotaMax = () => requestWithPoolIdGet('/web/project/v1/config/query', { key: 'crprj' });

// 常用说明
export const getCommonDesc = (data) => requestWithPoolIdGet('/web/admin/approval/v1/user/common-desc', data);

// 批量通过/驳回
export const batchProcess = (data) => requestWithPoolIdPost('/web/admin/approval/v1/user/batch-process', data);

// 获取批量通过/驳回 工单列表
export const getBatchList = (data) => requestWithPoolIdPost('/web/admin/approval/v1/user/list-content', data);
