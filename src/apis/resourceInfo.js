import { GET, POST, requestWithPoolId } from '@/request';
const { GET: requestWithPoolIdGet, POST: requestWithPoolIdPost } = requestWithPoolId;

//获取资源组管理详情
export const getResourceGroupDetail = (data) => requestWithPoolIdGet('/web/admin/resource/v1/manage/resource-group-detail', data);

//编辑资源组详情
export const editResourceGroupDetail = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/update-resource-group', data);

//删除资源组
export const deleteResourceGroupDetail = (data) => requestWithPoolIdGet('/web/admin/resource/v1/manage/delete-resource-group', data);

//资源组详情资源看板图
export const getOccupyDetailDiagram = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/get-detail-used-rate-graph', data);

//资源组详情资源使用情况
export const getGroupResourceUseRateDiagram = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/get-resource-used-rate-graph', data);

//节点详情资源占用情况
export const getNodeOccupyDiagram = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/get-node-resource-occupy-graph', data);

//节点详情资源使用情况
export const getNodeResourceUseRateDiagram = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/get-node-resource-use-rate-graph', data);
