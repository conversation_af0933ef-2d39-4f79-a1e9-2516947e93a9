import { GET, POST, requestWithPoolId, requestWithProjectId } from '@/request';
const { GET: requestWithPoolIdGET } = requestWithPoolId;
const { GET: requestWithProjectIdGET, POST: requestWithProjectIdPOST } = requestWithProjectId;
// 获取专属资源组列表
export const getPrivateResourceGroupList = () => requestWithProjectIdGET('/web/resource/v1/private-resource-group-list');

// 获取全量公共资源组列表
export const getPublicResourceGroupList = () => requestWithPoolIdGET('/web/resource/v1/public-resource-group-list');

// 获取资源组规格配比信息
export const getResourceGroupSpec = (data) => requestWithPoolIdGET('/web/resource/v1/resource-group-spec', data);

// 获取资源组详情信息
export const getResourceGroupDetail = (data) => requestWithProjectIdGET('/web/resource/v1/resource-group-detail', data);

// 获取资源组下节点使用情况列表
export const getResourceNodeList = (data) => requestWithProjectIdPOST('/web/resource/v1/resource-node-list', data);

// 获取资源池信息
export const getResourcePoolInfo = (data) => GET('/web/resource/v1/resource-pool-info', data);

// 获取某个项目下的资源配额
export const getQuotaForProject = (data) => requestWithProjectIdGET('/web/resource/v1/quota', data);
//获取全量公共资源组列表
// export const getPublicRslist = (data) => requestWithProjectIdGET('/web/resource/v1/public-resource-group-list', data);
//获取该项目可见的专属资源组列表
// export const getprivateRslist = (data) => requestWithProjectIdGET('/web/resource/v1/private-resource-group-list', data);
// 分页获取项目已关联公共资源组信息
export const getPublicGroupList = (data) => requestWithProjectIdGET('/web/resource/v1/project/public-resource-group', data);
export const getAdminPublicGroupList = (data) => requestWithPoolId.GET('/web/admin/resource/v1/board/get-public-resource-group', data);
//分页获取项目已关联专属资源组信息
export const getPrivateGroupList = (data) => requestWithProjectIdGET('/web/resource/v1/project/private-resource-group', data);
export const getAdminPrivateGroupList = (data) => requestWithPoolId.GET('/web/admin/resource/v1/board/get-private-resource-group', data);
// 分页获取资源组列表数据
export const getResourceGroup = (data) => requestWithPoolId.POST('/web/resource/v1/user/list-resource-group', data);

// 获取资源组加速卡类型
export const getResourceGroupGpuCardType = (data) => requestWithPoolId.GET('/web/resource/v1/gpu-card-type', data);

// 获取用户下所有的资源组简要信息
export const getListAllResourceGroup = () => requestWithProjectIdPOST('/web/resource/v1/user/list-all-resource-group');

//获取项目中资源配额使用详情
export const getUsedDetails = (data) => requestWithProjectIdGET('/web/resource/v1/quota/used-detail', data);
export const getAdminUsedDetails = (data) => requestWithPoolId.GET('/web/admin/resource/v1/board/get-quota-used-detail', data);
// 获取当前资源池下的所有资源组信息(需要有项目活动管理的admin权限)
export const getAdminListAllResourceGroup = () => requestWithPoolId.GET('/web/admin/resource/v1/manage/get-pool-all-resource-group')
// 获取某个资源组中项目活动占用量统计
export const getMostUsage = (data) => requestWithPoolId.GET('/web/admin/metering/v1/resgroup/most-usage', data);

// 用户侧获取全量已发布动态列表
export const getDynamicList = () => requestWithPoolId.POST('/web/display/v1/dynamic/getList');
