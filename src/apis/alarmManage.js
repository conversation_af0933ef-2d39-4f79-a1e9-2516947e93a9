import { requestWithPoolId } from '@/request/index';
import { EVENT_LEVEL_LIST, TRIGGER_TYPE_LIST, TRIGGER_MODE_LIST } from '@/constants/alarmMap.js';
const { POST, GET } = requestWithPoolId;

function disposeEnum(list = []) {
  if (!list || list.length === 0) return null;
  list = JSON.parse(JSON.stringify(list));
  let alertObjectConfigs = [];
  let alertEventConfigs = [];
  let eventLevel = [];
  let triggerType = [];
  let triggerMode = [];
  for (let i = list.length; i--; ) {
    list[i].text = list[i].alertModuleName;
    list[i].value = list[i].alertModuleId;
    if (!list[i]?.alertObjectConfigs || !list[i]?.alertObjectConfigs.length) continue;
    alertObjectConfigs = alertObjectConfigs.concat(list[i]?.alertObjectConfigs);
  }
  for (let y = alertObjectConfigs.length; y--; ) {
    const item = alertObjectConfigs[y];
    item.text = item.alertObjectConfigName;
    item.value = item.alertObjectConfigId;
    if (!item?.alertEventConfigs || !item?.alertEventConfigs.length) continue;
    alertEventConfigs = alertEventConfigs.concat(item?.alertEventConfigs.filter((v) => alertEventConfigs.every((vl) => v.alertEventConfigId !== vl.alertEventConfigId)));
  }
  for (let j = alertEventConfigs.length; j--; ) {
    const items = alertEventConfigs[j];
    items.text = items.alertEventName;
    items.value = items.alertEventConfigId;
  }
  for (let i = alertEventConfigs.length; i--; ) {
    eventLevel = eventLevel.concat(alertEventConfigs[i].eventLevel.filter((v) => !eventLevel.includes(v)));
    triggerType = triggerType.concat(alertEventConfigs[i].triggerType.filter((v) => !triggerType.includes(v)));
    triggerMode = triggerMode.concat(alertEventConfigs[i].triggerMode.filter((v) => !triggerMode.includes(v)));
  }
  return {
    MONITORING_MODULES_LIST: list, // 监控模块
    MONITORIN_OBJECT_LIST: alertObjectConfigs, // 监控对象
    ALARM_INCIDENT_LIST: alertEventConfigs, // 告警事件
    EVENT_LEVEL_LIST: EVENT_LEVEL_LIST.filter((v) => eventLevel.includes(v.value)), // 事件等级
    TRIGGER_TYPE_LIST: TRIGGER_TYPE_LIST.filter((v) => triggerType.includes(v.value)), // 触发方式
    TRIGGER_MODE_LIST: TRIGGER_MODE_LIST.filter((v) => triggerMode.includes(v.value)), // 触发模式
  };
}
// 告警配置
export const getAlarmEnumApi = async () => {
  const res = await GET('/web/admin/alert/v1/ruler/settings');
  res.disposeEnum = disposeEnum(res.data);
  return res;
};
// 告警记录列表
export const getAlarmRecordListApi = async (payload) => {
  return POST('/web/admin/alert/v1/record/list', payload);
};
// 告警规则列表
export const getAlarmRulesListApi = async (payload) => {
  return POST('/web/admin/alert/v1/ruler/list', payload);
};
// 通知组列表
export const getAlarmNoticeListApi = async (payload) => {
  return POST('/web/admin/alert/v1/group/list', payload);
};

// 新建告警规则
export const createAlarmRuleApi = async (payload) => {
  return POST('/web/admin/alert/v1/ruler/create', payload);
};
// 编辑告警规则
export const editAlarmRuleApi = async (payload) => {
  return POST('/web/admin/alert/v1/ruler/edit', payload);
};
// 删除告警规则
export const deleteAlarmRuleApi = async (payload) => {
  return GET('/web/admin/alert/v1/ruler/delete', payload);
};
// 告警规则详情
export const getAlarmRuleDetailApi = async (payload) => {
  return GET('/web/admin/alert/v1/ruler/detail', payload);
};
// 告警记录详情
export const getAlarmRecordDetailApi = async (payload) => {
  return GET('/web/admin/alert/v1/record/detail', payload);
};
// 新建通知组
export const createNoticeGroupApi = async (payload) => {
  return POST('/web/admin/alert/v1/group/create', payload);
};
// 编辑通知组
export const editNoticeGroupApi = async (payload) => {
  return POST('/web/admin/alert/v1/group/edit', payload);
};
// 删除通知组
export const deleteNoticeGroupApi = async (payload) => {
  return GET('/web/admin/alert/v1/group/delete', payload);
};
// 精准匹配用户
export const getNoticeUserApi = async (payload) => {
  return GET('/web/auth/v1/user/getUserByName', payload);
};
// 详情获取资源组列表数据
export const getResourceGroupApi = async (payload) => {
  return POST('/web/admin/resource/v1/manage/get-resource-group-list', payload);
};
// 详情获取节点列表数据
export const getNodeApi = async (payload) => {
  return POST('/web/admin/resource/v1/manage/get-node-list', payload);
};

// 停用/启用规则
export const updateRuleStatusApi = async (payload) => {
  return GET('/web/admin/alert/v1/ruler/update-status', payload);
};
// 校验告警规则名称
export const checkAlarmRuleNameApi = async (payload) => {
  return GET('/web/admin/alert/v1/ruler/check-ruler-name', payload);
};
// 校验通知组名称
export const checkNoticeGroupNameApi = async (payload) => {
  return GET('/web/admin/alert/v1/ruler/check-group-name', payload);
};
