import { requestWithProjectId } from '@/request';
const { GET: requestWithProjectIdGET, POST: requestWithProjectIdPOST } = requestWithProjectId;

// 分页查询提示词模板列表（含预置模板、自定义模板）
export const getPromptManageList = (data) => requestWithProjectIdPOST('/web/prompt/v1/list', data);

// 查询提示词模板列表标签集合（含预置模板、自定义模板）
export const getPromptTagsList = (data) => requestWithProjectIdGET('/web/prompt/v1/searchTags', data);

// 查询提示词模板详情（含预置模板、自定义模板）
export const getPromptManageDetail = (data) => requestWithProjectIdGET('/web/prompt/v1/get', data);

// 创建提示词模板（含自定义模板）
export const createPromptManage = (data) => requestWithProjectIdPOST('/web/prompt/v1/create', data);

// 修改提示词模板（含自定义模板）
export const updatePromptManage = (data) => requestWithProjectIdPOST('/web/prompt/v1/update', data);

// 删除提示词模板（含自定义模板）
export const deletePromptManage = (data) => requestWithProjectIdPOST('/web/prompt/v1/delete', data);

// 获取提示词模板创建标签列表（含预置模板、自定义模板）
export const getPromptManageTagsList = (data) => requestWithProjectIdGET('/web/prompt/tag/v1/list', data);

// 创建提示词模板标签（含自定义模板）
export const createPromptManageTag = (data) => requestWithProjectIdPOST('/web/prompt/tag/v1/create', data);

// 删除提示词模板标签 （含自定义模板）
export const deletePromptManageTag = (data) => requestWithProjectIdPOST('/web/prompt/tag/v1/delete', data);

// 优化提示词
export const optimizePromptManage = (data) => requestWithProjectIdPOST('/web/prompt/v1/optimize', data);
