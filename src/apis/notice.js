// 运营中心公告管理相关接口
import { requestWithPoolId } from '@/request/index';
const { POST: requestWithPoolIdPOST, GET: requestWithPoolIdGET } = requestWithPoolId;

// 用户端获取变更前弹窗信息
// export const popupMessage = (data) => requestWithPoolIdGET('/web/settings/v1/notice/pop-up-message', data);
export const popupMessage = (data) => requestWithPoolIdGET('/web/display/v1/notice/pop-up-message', data);

// 用户端获取变更中维护页面信息
// export const noticeMessage = (data) => requestWithPoolIdGET('/web/settings/v1/notice/notice-message', data);
export const noticeMessage = (data) => requestWithPoolIdGET('/web/display/v1/notice/notice-message', data);

// 控制变更前弹窗开关
// export const setPopupSwitch = (data) => requestWithPoolIdPOST('/web/admin/settings/v1/notice/set-pop-up-switch', data);

// 设置变更前弹窗信息
export const setPopupMessage = (data) => requestWithPoolIdPOST('/web/admin/display/v1/notice/set-pop-up-message', data);

// 运管端控制变更中维护页面开关
// export const setNoticeSwtich = (data) => requestWithPoolIdPOST('/web/admin/settings/v1/notice/set-notice-switch', data);

// 运管端设置变更中维护页面信息
export const setNoticeMessage = (data) => requestWithPoolIdPOST('/web/admin/display/v1/notice/set-notice-message', data);

// 运管端获取变更前弹窗状态
export const getPopupSwitch = (data) => requestWithPoolIdGET('/web/admin/display/v1/notice/get-pop-up-switch', data);

// 运管端获取变更前弹窗信息
export const getPopupMessage = (data) => requestWithPoolIdGET('/web/admin/display/v1/notice/get-pop-up-message', data);

// 运管端获取变更中维护页面状态
export const getNoticeSwitch = (data) => requestWithPoolIdGET('/web/admin/display/v1/notice/get-notice-switch', data);

// 运管端获取变更中维护页面信息
export const getNoticeMessage = (data) => requestWithPoolIdGET('/web/admin/display/v1/notice/get-notice-message', data);

// 运管端获取概览动态列表
export const getOveriewList = (data) => requestWithPoolIdPOST('/web/admin/display/v1/dynamic/get-list', data);

// 运管端新建概览信息
export const getAddOveriew = (data) => requestWithPoolIdPOST('/web/admin/display/v1/dynamic/add', data);

// 运管端编辑概览信息
export const getUpdateOveriew = (data) => requestWithPoolIdPOST('/web/admin/display/v1/dynamic/update', data);

// 运管端概览信息动态上架
export const getPublishOveriew = (data) => requestWithPoolIdGET('/web/admin/display/v1/dynamic/publish', data);

// 运管端概览信息动态下架
export const getUnPublishOveriew = (data) => requestWithPoolIdGET('/web/admin/display/v1/dynamic/un-publish', data);

// 运管端概览信息动态删除
export const getDeleteOveriew = (data) => requestWithPoolIdGET('/web/admin/display/v1/dynamic/delete', data);
