import { GET, POST, requestWithPoolId, requestWithProjectId } from '@/request';
const { GET: requestWithProjectIdGET, POST: requestWithProjectIdPOST } = requestWithProjectId;
const { POST: requestWithPoolIdPOST, GET: requestWithPoolIdGET } = requestWithPoolId;

// 获取挂载项目空间列表
export const getProjectList = (data) => requestWithPoolIdPOST('/web/admin/storage/v1/getProjectList', data);

// 获取已挂载外部存储目录列表
export const getExternalList = (data) => requestWithPoolIdPOST('/web/admin/storage/v1/getExternalList', data);

// 获取项目列表使用量
export const getProjectUsages = (data) => requestWithPoolIdPOST('/web/admin/storage/v1/getProjectUsages', data);

// 新增或修改挂载目录
export const addOrUpdate = (data) => requestWithPoolIdPOST('/web/admin/storage/v1/addOrUpdate', data);

// 新增或修改挂载目录
export const getProjectDetailList = (data) => requestWithPoolIdPOST('/web/admin/storage/v1/list', data);

// 获取项目列表基本信息
export const getProjectBaseInfo = (data) => requestWithPoolIdGET('/web/admin/storage/v1/getProjectBaseInfo', data);

// 获取已挂载存储基本信息
export const getExternalBaseInfo = (data) => requestWithPoolIdGET('/web/admin/storage/v1/getExternalBaseInfo', data);

// 删除挂载项目
export const deleteProject = (data) => requestWithPoolIdGET('/web/admin/storage/v1/delete', data);
