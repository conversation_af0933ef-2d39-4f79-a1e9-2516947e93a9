import { requestWithProjectId } from '@/request/index';
const { POST, GET } = requestWithProjectId;

// 获取列表
export const getEvaluateTasks = (data) => POST(`/web/evaluate/v1/tasks`, data);

// 获取任务详情
export const getEvaluateTaskInfo = ({ id }) => GET(`/web/evaluate/v1/tasks/${id}`);

// 停止单个任务
export const stopMission = ({ id }) => POST(`/web/evaluate/v1/task/${id}/stop`);

// 删除任务
export const deleteMission = (data) => POST('/web/evaluate/v1/tasks/delete', data);

// 查询评估详情
export const getTaskDetail = (data) => POST('/web/evaluate/v1/detail', data);

// 查询复制评估详情
export const getCopyDetail = (data) => GET('/web/evaluate/v1/tasks/replicate', data);

// 查询所需资源
export const getModelSource = (data) => GET('/web/evaluate/v1/get-model-resource', data);

// 编辑任务描述
export const editTaskDesc = (data) => POST('/web/evaluate/v1/tasks/update', data);

// 查询评估日志
export const getTaskLog = (data) => POST('/web/evaluate/v1/tasks/log', data);

// 查询评估报告
export const getEvaluateReport = (data) => POST('/web/evaluate/v1/report', data);

// 创建评估任务
export const createTask = (data) => POST('/web/evaluate/v1/tasks/create', data);

// 获取任务名称
export const getTaskName = (data) => GET('/web/evaluate/v1/get-task-name', data);

// 获取自定义模型列表
export const getModelList = (data) => GET('/web/model/v1/manage/list', data);

// 获取数据集列表
export const getDataList = (data) => GET('/web/model/v1/manage/dict', data);

// 获取资源组信息
export const getResourceGroup = (data) => GET('/web/model/v1/manage/dict', data);

//获取裁判员模型数据
export const getArbModelInfo = () => GET('/web/evaluate/v1/arbitrator-model-info');

// 查询人工评估详情

export const getManualTask = (data) => POST('/web/evaluate/v1/manual/detail-all', data);

export const getOneEvaDetail = (data) => POST('/web/evaluate/v1/manual/detail', data);

export const updateEvalute = (data) => POST('/web/evaluate/v1/manual/detail-update', data);

export const submitEvalute = (data) => POST('/web/evaluate/v1/tasks/submit', data);

export const getOneEvaDetailWidthId = (data) => GET('/web/evaluate/v1/manual/detail-one', data);

export const getResImage = (data) => GET('/web/evaluate/v1/image', data);
