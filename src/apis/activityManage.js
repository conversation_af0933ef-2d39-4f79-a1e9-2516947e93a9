import { TASK_TYPE } from '@/constants/activityManage';
import { POST, GET, requestWithPoolId } from '@/request/index';

const { POST: requestWithPoolIdPOST, GET: requestWithPoolIdGET } = requestWithPoolId;

//各模块对应的接口地址
// 配置统计接口地址
export const STATISTIC_URL_MAP = {
  [TASK_TYPE.DEV]: '/web/admin/develop/v1/count',
  [TASK_TYPE.TASK]: '/web/admin/task/v1/count',
  [TASK_TYPE.INFE]: '/web/admin/serving/v1/count',
  [TASK_TYPE.DAP]: '/web/admin/finetune/v1/INC_PRETRAIN/count',
  [TASK_TYPE.SFT]: '/web/admin/finetune/v1/SFT/count',
  [TASK_TYPE.COMPRESS]: '/web/model/admin/compress/v1/count',
  [TASK_TYPE.EVALUATION]: '/web/admin/evaluate/v1/count',
  [TASK_TYPE.ANALYSIS]: '/web/analysetask/admin/v1/count',
  [TASK_TYPE.CLEAN]: '/web/processor/admin/clean/v1/count',
  [TASK_TYPE.ANGMENTATION]: '/web/processor/admin/enhance/v1/count',
  [TASK_TYPE.DPO]: '/web/admin/finetune/v1/DPO/count',
};

export const REQUEST_URL_MAP = {
  [TASK_TYPE.DEV]: {
    TASK_LIST: '/web/admin/develop/v1/list',
    STOP: '/web/admin/develop/v1/batch/stop',
    DETAIL: '/web/admin/develop/v1/detail',
    OPERATE_LIST: '/web/admin/develop/v1/operate/list',
    SEND: '/web/admin/develop/v1/send',
    CONTACT: '/web/admin/develop/v1/user/contact',
    EVENT_LIST: '/web/admin/develop/v1/events',
    EXPORT: '/web/admin/develop/v1/list/export',
  },
  [TASK_TYPE.TASK]: {
    TASK_LIST: '/web/admin/task/v1/list',
    STOP: '/web/admin/task/v1/batch/stop',
    DETAIL: '/web/admin/task/v1/detail',
    INSTANCE_LIST: '/web/admin/task/v1/pod/list',
    OPERATE_LIST: 'web/admin/task/v1/operate/list',
    SEND: '/web/admin/task/v1/send',
    CONTACT: '/web/admin/task/v1/user/contact',
    EVENT_LIST: '/web/admin/task/v1/events',
    EXPORT: '/web/admin/task/v1/list/export',
  },
  [TASK_TYPE.INFE]: {
    TASK_LIST: '/web/admin/serving/v1/list',
    STOP: '/web/admin/serving/v1/batch/stop',
    DETAIL: '/web/admin/serving/v1/detail',
    INSTANCE_LIST: '/web/admin/serving/v1/pod/list',
    OPERATE_LIST: 'web/admin/serving/v1/operate/list',
    SEND: '/web/admin/serving/v1/send',
    CONTACT: '/web/admin/serving/v1/user/contact',
    EVENT_LIST: '/web/admin/serving/v1/events',
    EXPORT: '/web/admin/serving/v1/list/export',
  },
  [TASK_TYPE.DAP]: {
    TASK_LIST: '/web/admin/finetune/v1/INC_PRETRAIN/list',
    STOP: '/web/admin/finetune/v1/INC_PRETRAIN/batch/stop',
    DETAIL: '/web/admin/finetune/v1/INC_PRETRAIN/detail',
    OPERATE_LIST: 'web/admin/finetune/v1/INC_PRETRAIN/operate/list',
    SEND: '/web/admin/finetune/v1/INC_PRETRAIN/send',
    CONTACT: '/web/admin/finetune/v1/INC_PRETRAIN/user/contact',
    EXPORT: 'web/admin/finetune/v1/INC_PRETRAIN/list/export',
  },
  [TASK_TYPE.SFT]: {
    TASK_LIST: '/web/admin/finetune/v1/SFT/list',
    STOP: '/web/admin/finetune/v1/SFT/batch/stop',
    DETAIL: '/web/admin/finetune/v1/SFT/detail',
    OPERATE_LIST: 'web/admin/finetune/v1/SFT/operate/list',
    SEND: '/web/admin/finetune/v1/SFT/send',
    CONTACT: '/web/admin/finetune/v1/SFT/user/contact',
    EXPORT: 'web/admin/finetune/v1/SFT/list/export',
  },
  [TASK_TYPE.COMPRESS]: {
    TASK_LIST: '/web/model/admin/compress/v1/list',
    STOP: '/web/model/admin/compress/v1/batch/stop',
    DETAIL: '/web/model/admin/compress/v1/detail',
    OPERATE_LIST: 'web/model/admin/compress/v1/operate/list',
    SEND: '/web/model/admin/compress/v1/send',
    CONTACT: '/web/model/admin/compress/v1/user/contact',
    EXPORT: '/web/model/admin/compress/v1/list/export',
  },
  [TASK_TYPE.EVALUATION]: {
    TASK_LIST: '/web/admin/evaluate/v1/list',
    STOP: '/web/admin/evaluate/v1/batch/stop',
    DETAIL: '/web/admin/evaluate/v1/detail',
    OPERATE_LIST: 'web/admin/evaluate/v1/operate/list',
    SEND: '/web/admin/evaluate/v1/send',
    CONTACT: '/web/admin/evaluate/v1/user/contact',
    EXPORT: '/web/admin/evaluate/v1/list/export',
  },
  // 数据解析、数据清洗、数据增强V2.0
  [TASK_TYPE.ANALYSIS]: {
    TASK_LIST: '/web/analysetask/admin/v1/list',
    STOP: '/web/analysetask/admin/v1/batch/stop',
    DETAIL: '/web/analysetask/admin/v1/detail',
    OPERATE_LIST: '/web/analysetask/admin/v1/operate/list',
    SEND: '/web/analysetask/admin/v1/send',
    CONTACT: '/web/analysetask/admin/v1/user/contact',
    EXPORT: '/web/analysetask/admin/v1/excelExport',
  },
  [TASK_TYPE.CLEAN]: {
    TASK_LIST: '/web/processor/admin/clean/v1/list',
    STOP: '/web/processor/admin/clean/v1/batch/stop',
    DETAIL: `/web/processor/admin/clean/v1/detail`,
    OPERATE_LIST: '/web/processor/admin/clean/v1/operate/list',
    SEND: '/web/processor/admin/clean/v1/send',
    CONTACT: '/web/processor/admin/process/v1/user/contact',
    EXPORT: '/web/processor/admin/clean/v1/list/export',
  },
  [TASK_TYPE.ANGMENTATION]: {
    TASK_LIST: '/web/processor/admin/enhance/v1/list',
    STOP: '/web/processor/admin/enhance/v1/batch/stop',
    DETAIL: `/web/processor/admin/enhance/v1/detail`,
    OPERATE_LIST: '/web/processor/admin/enhance/v1/operate/list',
    SEND: '/web/processor/admin/enhance/v1/send',
    CONTACT: '/web/processor/admin/process/v1/user/contact',
    EXPORT: '/web/processor/admin/enhance/v1/list/export',
  },
  // 偏好对齐
  [TASK_TYPE.DPO]: {
    TASK_LIST: '/web/admin/finetune/v1/DPO/list',
    STOP: '/web/admin/finetune/v1/DPO/batch/stop',
    DETAIL: '/web/admin/finetune/v1/DPO/detail',
    OPERATE_LIST: '/web/admin/finetune/v1/DPO/operate/list',
    SEND: '/web/admin/finetune/v1/DPO/send',
    CONTACT: '/web/admin/finetune/v1/DPO/user/contact',
    EXPORT: 'web/admin/finetune/v1/DPO/list/export',
  },
};
// 获取 开发环境/训练任务/推理服务列表
export const getDataList = (sourceType, data) => requestWithPoolIdPOST(REQUEST_URL_MAP[sourceType].TASK_LIST, data);

// 批量停止
export const batchStop = (sourceType, data) => requestWithPoolIdPOST(REQUEST_URL_MAP[sourceType].STOP, data);

// 获取详情
export const getDetail = (sourceType, data) => requestWithPoolIdGET(REQUEST_URL_MAP[sourceType].DETAIL, data);
// 数据清洗、增强获取详情
export const getDetailInfo = (sourceType, data) => requestWithPoolIdGET(`${REQUEST_URL_MAP[sourceType].DETAIL}/${data}`);

// 获取实例列表
export const getInstanceList = (sourceType, data) => requestWithPoolIdPOST(REQUEST_URL_MAP[sourceType].INSTANCE_LIST, data);

// 获取事件列表
export const getEventList = (sourceType, data) => requestWithPoolIdPOST(REQUEST_URL_MAP[sourceType].EVENT_LIST, data);

// 获取操作列表
export const getOperateList = (sourceType, data) => requestWithPoolIdPOST(REQUEST_URL_MAP[sourceType].OPERATE_LIST, data);

// 发送短信、邮件或站内信
export const send = (sourceType, data) => requestWithPoolIdGET(REQUEST_URL_MAP[sourceType].SEND, data);

// 获取开发环境总数
export const getDevTotal = () => requestWithPoolIdGET('/web/admin/develop/v1/count');
// 获取训练任务总数
export const getTaskTotal = () => requestWithPoolIdGET('/web/admin/task/v1/count');
// 获取推理服务总数
export const getSerivceTotal = () => requestWithPoolIdGET('/web/admin/serving/v1/count');

// 获取用户的手机号和邮箱信息
export const getContact = (sourceType, data) => requestWithPoolIdGET(REQUEST_URL_MAP[sourceType].CONTACT, data);

// 获取增强详情-任务总结
export const getResultList = (id, data) => requestWithPoolIdPOST(`web/processor/admin/enhance/v1/task/${id}/result_show`, data);
// 获取清洗/增强详情-任务总结 v201版本接口替换
export const getFileServiceUrl = (data, headers) => GET('/web/storage/v1/fileServiceUrl', data, { headers });
export const getFileContent = (url, data, headers) => POST(url, data, { headers });

// 数据增强为多模态时任务总结接口
export const getMultiContent = (url, data, headers) => POST(url, data, { headers });
// 下载图片
export const downloadImage = (url, data, headers) => GET(url, data, { headers });

// 获取增强解析-解析结果
export const getAnalysisList = (data) => requestWithPoolIdPOST(`/web/analysetask/admin/v1/task/listFileDetail`, data);
// 获取增强解析-转存结果
export const getTransferList = (data) => requestWithPoolIdPOST(`/web/analysetask/admin/v1/transfer/query`, data);

// 更新训练任务的优先级
export const updatePriority = (data) => requestWithPoolIdPOST(`/web/admin/task/v1/update-priority`, data);
