<template>
  <jt-container class="announcement">
    <sub-header :bread-crumb="breadCrumb" class="announcement-header"></sub-header>
    <page-banner />
    <div class="list-container">
      <a-tabs :active-key="currentTab" @change="handleChangeTab">
        <a-tab-pane :key="ANNOUNCEMENT_TABS.UPGRADE" tab="维护升级页面">
          <a-flex v-if="ANNOUNCEMENT_TABS.UPGRADE" style="margin-top: 20px" justify="space-between">
            <a-descriptions class="descriptions-container" :column="1" :colon="false">
              <a-descriptions-item :label-style="labelStyle" label="标题：">{{ noticeMessage.title || '无' }}</a-descriptions-item>
              <a-descriptions-item :label-style="labelStyle" label="状态：">
                <jt-tag class="tag announcement-tag-status" :color="noticeMessage.status ? 'green' : ''"> {{ noticeMessage.status ? '已开启' : '已关闭' }} </jt-tag>
              </a-descriptions-item>
              <a-descriptions-item :label-style="labelStyle" label="内容：">{{ noticeMessage.content || '无' }}</a-descriptions-item>
              <a-descriptions-item :label-style="labelStyle" label="预览">
                <upgrade-preview v-bind="noticeMessage" class="preview-iframe" />
              </a-descriptions-item>
            </a-descriptions>
            <!-- <upgrade :title="noticeMessage.title" :content="noticeMessage.content" class="preview-iframe upgrade" preview /> -->
          </a-flex>
        </a-tab-pane>
        <a-tab-pane :key="ANNOUNCEMENT_TABS.POPUP" tab="系统公告弹窗">
          <a-flex v-if="ANNOUNCEMENT_TABS.POPUP" tyle="margin-top: 20px" justify="space-between">
            <a-descriptions class="descriptions-container" :column="1" :colon="false">
              <a-descriptions-item :label-style="labelStyle" label="标题：">{{ popupMessage.title || '无' }}</a-descriptions-item>
              <a-descriptions-item :label-style="labelStyle" label="状态：">
                <jt-tag class="tag announcement-tag-status" :color="popupMessage.status ? 'green' : ''"> {{ popupMessage.status ? '已开启' : '已关闭' }} </jt-tag>
              </a-descriptions-item>
              <a-descriptions-item :label-style="labelStyle" label="内容：">{{ popupMessage.content || '无' }}</a-descriptions-item>
              <a-descriptions-item :label-style="labelStyle" label="预览">
                <sys-preview class="preview-iframe" preview v-bind="popupMessage" />
              </a-descriptions-item>
            </a-descriptions>
          </a-flex>
        </a-tab-pane>
        <a-tab-pane :key="ANNOUNCEMENT_TABS.OVERVIEW" tab="概览动态信息">
          <overiew-list v-if="ANNOUNCEMENT_TABS.OVERVIEW" ref="overviewRef" :search-text="searchText" :edit-auth="editAuth" />
        </a-tab-pane>
        <template #rightExtra>
          <a-space v-if="currentTab == 'overiew'">
            <jt-reload-icon-btn @click="handelReload" />
            <a-input v-model:value="searchText" type="text" class="input" autocomplete="off" allow-clear placeholder="请输入动态标题/链接">
              <template #prefix>
                <jt-icon type="iconsousuo" class="search-icon" style="color: rgba(0, 20, 26, 0.25)"></jt-icon>
              </template>
            </a-input>
            <a-button v-if="editAuth" type="primary" class="kl-create-btn btn-vertical-align" @click="handleCreate">
              新建动态
              <template #icon>
                <jt-icon type="icontianjia" class="add-icon"></jt-icon>
              </template>
            </a-button>
          </a-space>
          <a-space v-else>
            <a-button @click="gotoPreview(currentTab)">
              <jt-icon type="iconeye1" />
              预览
            </a-button>
            <a-button type="primary" ghost @click="gotoEdit(currentTab)">
              <jt-icon type="iconbianji" />
              编辑
            </a-button>
          </a-space>
        </template>
      </a-tabs>
      <overiew-create v-model:open="createOveriewOpen" @ok="refreshTable" />
    </div>
  </jt-container>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

import { gotoNewTarget } from '@/utils';
import { noticeApi } from '@/apis';
import { UPGRAD_PREVIEW_STORAGE_KEY, POPUP_PREVIEW_STORAGE_KEY, ANNOUNCEMENT_TABS, MGT_NOTICE_EDIT_AUTH } from '@/constants/announcement.js';
import { setItem } from '@/utils/storageUtil';
import overiewCreate from './components/overiew-create.vue';
import overiewList from './overiew-list.vue';
import SubHeader from '@/components/subHeader.vue';
import pageBanner from './pageBanner.vue';
import sysPreview from './popup-preview.vue';
import upgradePreview from './upgrade-preview.vue';
import { checkPermissionByApvAndMgtItem } from '@/keycloak';

const defaultActiveKey = '维护升级页面';

const router = useRouter();
const route = useRoute();
const activeKey = ref('');
activeKey.value = route.query.activeKey || defaultActiveKey;
watch(activeKey, () => {
  router.push({ query: { activeKey: activeKey.value } });
});
const noticeMessage = ref({});
const noticeLoading = ref(false);
const popupMessage = ref({});
const popupLoading = ref(false);
const routeTab = route.query.tab;
const overviewRef = ref(null);
const currentTab = ref(routeTab || ANNOUNCEMENT_TABS.UPGRAD);
const searchText = ref(route.query.searchText || '');
const editAuth = checkPermissionByApvAndMgtItem(MGT_NOTICE_EDIT_AUTH);
const createOveriewOpen = ref(false);
const breadCrumb = computed(() => {
  if (route.meta.header && route.meta.header.length > 1) {
    const breadCrumbList = route.meta.header;
    if (route.query.groupId) {
      breadCrumbList.map((x) => {
        if (x.query?.groupId !== undefined) {
          x.query.groupId = route.query.groupId;
        }
      });
    }
    return breadCrumbList;
  } else {
    return [];
  }
});

const handleCreate = () => {
  createOveriewOpen.value = true;
};

onMounted(async () => {
  getNoticeStatus();
});

const getNoticeStatus = async () => {
  noticeLoading.value = true;
  try {
    const noticeSwitchRes = await noticeApi.getNoticeSwitch();
    const noticeMessageRes = await noticeApi.getNoticeMessage();
    noticeMessage.value = { ...noticeMessageRes.data, status: noticeSwitchRes.data };
  } catch (err) {
    message.error('获取变更中维护页面内容时出错，请稍后再试');
    throw new Error(err);
  }
  noticeLoading.value = false;
};
const getPopupStatus = async () => {
  popupLoading.value = true;
  try {
    const popupSwitchRes = await noticeApi.getPopupSwitch();
    const popupMessageRes = await noticeApi.getPopupMessage();
    popupMessage.value = { ...popupMessageRes.data, status: popupSwitchRes.data };
  } catch (err) {
    message.error('获取变更前弹窗内容时出错，请稍后再试');
    throw new Error(err);
  }
  popupLoading.value = false;
};
const gotoPreview = (type) => {
  let url = '';
  if (type === 'upgrade') {
    url = router.resolve({ path: '/upgrade', query: { preview: 1 } });
    setItem(UPGRAD_PREVIEW_STORAGE_KEY, { ...noticeMessage.value });
  } else if (type === 'popup') {
    url = router.resolve({ path: '/', query: { preview: 1 } });
    setItem(POPUP_PREVIEW_STORAGE_KEY, { ...popupMessage.value });
  }
  gotoNewTarget(url);
};
const gotoEdit = (type) => {
  if (type === 'upgrade') {
    router.push({
      path: '/announcement/notice-edit',
      query: {
        tab: type,
      },
    });
  } else if (type === 'popup') {
    router.push({
      path: '/announcement/popup-edit',
      query: {
        tab: type,
      },
    });
  }
};

const handleChangeTab = (key) => {
  currentTab.value = key;
  searchText.value = '';
  router.replace({ query: { tab: key } });
  nextTick(() => {
    if (currentTab.value === ANNOUNCEMENT_TABS.UPGRADE) {
      getNoticeStatus();
    } else if (currentTab.value === ANNOUNCEMENT_TABS.POPUP) {
      getPopupStatus();
    } else if (currentTab.value === ANNOUNCEMENT_TABS.OVERVIEW) {
      overviewRef.value.getTableInfo();
    }
  });
};

const handelReload = () => {
  if (currentTab.value === ANNOUNCEMENT_TABS.OVERVIEW) {
    overviewRef.value.getTableInfo();
  }
};
const refreshTable = () => {
  currentTab.value = ANNOUNCEMENT_TABS.OVERVIEW;
  overviewRef.value.getTableInfo();
};
</script>

<style lang="less" scoped>
@import './common.less';
.button {
  width: 120px;
  height: 32px;
  margin-top: 32px;
}
/deep/.ant-descriptions-row > td {
  padding-bottom: 16px;
}
.descriptions-container {
  width: 66%;
}
.preview-iframe {
  width: 285px;
  height: 160px;
  overflow: hidden;
  :deep(.main),
  :deep(.preview-mask),
  :deep(.sys-anno-wrap) {
    transform-origin: 0 0;
    transform: scale(0.2);
    margin-top: 0;
    width: calc(285px * 5);
    height: calc(160px * 5);
    .content {
      height: calc(100% - 134px);
    }
  }
  :deep(.sys-anno-wrap) {
    .mask {
      width: 100%;
      height: 100%;
    }
    .sys-anno-content {
      top: 31%;
    }
    .sider,
    .header {
      z-index: 0;
    }
  }
}
.announcement-tag-status {
  width: 42px;
  height: 22px;
  line-height: 22px;
}
.list-container {
  background-color: white;
  padding: 20px;
}
</style>
