<template>
  <div class="form-box">
    <detail-sub-header title="基础配置" style="margin-bottom: 20px" :size="14"></detail-sub-header>
    <a-form ref="formRef" :model="form" :label-col="labelCol" :rules="rules">
      <!-- 体验类型 -->
      <a-form-item label="体验类型" name="experienceType">
        <a-radio-group v-model:value="form.experienceType" style="margin-top: 6px" @change="typeChange">
          <a-radio value="0" style="margin-right: 20px">文本对话</a-radio>
          <a-radio value="1" style="margin-right: 20px">图生文</a-radio>
          <a-radio value="2">文生图</a-radio>
        </a-radio-group>
        <a-collapse v-model:activeKey="activeKey" :bordered="false" collapsible="icon" expand-icon-position="end" class="exp-collapse">
          <template #expandIcon="{ isActive }">
            <span v-if="isActive" class="coll-text">收起</span>
            <span v-else class="coll-text">展开</span>
            <span class="icon-box">
              <jt-icon type="iconzhan<PERSON>" class="iconfont" :rotate="isActive ? 180 : 0" />
            </span>
          </template>
          <a-collapse-panel key="1" :style="customStyle">
            <template #header>
              <span>接口类型</span>
              <span class="type">POST</span>
            </template>
            <div>
              <a-tabs v-model:activeKey="tabActiveKey" type="card">
                <a-tab-pane key="1" tab="请求消息体">
                  <div class="request-item" v-html="requestCode"></div>
                </a-tab-pane>
                <a-tab-pane key="2" tab="响应样例">
                  <!-- <div id="experienceResponse"></div> -->
                  <div class="request-item" v-html="responseCode"></div>
                </a-tab-pane>
              </a-tabs>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </a-form-item>
      <a-form-item label="调用服务地址" name="serviceUrl" class="big-gap">
        <a-textarea v-model:value="form.serviceUrl" :auto-size="{ minRows: 1, maxRows: 5 }" placeholder="请输入已部署成功的推理服务调用地址" style="width: 650px" />
      </a-form-item>
      <a-form-item label="AppCode" name="appCode" class="big-gap">
        <a-textarea v-model:value="form.appCode" :auto-size="{ minRows: 5, maxRows: 8 }" placeholder="请输入已关联对应服务的应用密钥" style="width: 650px" />
      </a-form-item>
    </a-form>
    <div>
      <detail-sub-header title="可调参数" style="margin: 16px 0px" :size="14"></detail-sub-header>
      <a-table :scroll="{ y: 1000, x: 1300 }" :columns="columns" :data-source="adjustableParamsConfig" :pagination="false" row-key="id" :expand-icon-as-cell="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'paramsName'">
            <div>
              <span class="params-name">{{ record.paramsName }}</span>
              <a-switch v-model:checked="record.paramsSwitch" checked-children="开" un-checked-children="关" style="margin-left: 26px" />
            </div>
          </template>
          <template v-if="column.key === 'valueRange'">
            <div v-if="record.paramsName === '系统人设'">
              <span>-</span>
            </div>
            <div v-else :class="record.error ? 'flex-item' : ''">
              <a-input v-model:value="record.valueRange" :status="record.error ? 'error' : ''" :disabled="!record.paramsSwitch" @change="valueRangeChange(record)" @blur="valueRangeBlur(record)"></a-input>
              <p v-if="record.error" class="error-text">{{ errorText }}</p>
            </div>
          </template>
          <template v-if="column.key === 'defaultValue'">
            <span v-if="record.paramsName === '系统人设'">你是一个乐于解答各种问题的助手，你的任务是为用户提供专业、准确、有见地的建议</span>
            <a-input-number v-else-if="record.paramsName === 'max_tokens'" id="inputNumber" v-model:value="record.defaultValue" :disabled="!record.paramsSwitch" :step="record.minStep" :min="record.min" :max="record.max" :precision="0" />
            <a-input-number v-else-if="record.paramsName === 'step'" id="inputNumber" v-model:value="record.defaultValue" :disabled="!record.paramsSwitch" :step="record.minStep" :min="record.min" :max="record.max" :precision="0" />
            <a-input-number v-else id="inputNumber" v-model:value="record.defaultValue" :disabled="!record.paramsSwitch" :step="record.minStep" :min="record.min" :max="record.max" :precision="2" />
          </template>
          <template v-if="column.key === 'minStep'">
            <span v-if="record.paramsName === '系统人设'">-</span>
            <span v-else>{{ record.minStep }}</span>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup>
import cloneDeep from 'lodash/cloneDeep';
import isEmpty from 'lodash/isEmpty';
import { urlregex } from '@/constants/regex.js';
import detailSubHeader from '@/components/detail-sub-header';
import { onMounted } from 'vue';
import { isNumeric } from '@/utils/number.js';

const props = defineProps({
  defaultData: {
    type: Object,
    default: () => {},
  },
});
// form表单相关
const labelCol = {
  style: {
    width: '120px',
  },
};
const errorText = ref('');
const isValidate = ref(false);
const formRef = ref(null);
const form = ref({
  experienceType: '0',
  serviceUrl: '',
  appCode: '',
});
const adjustableParamsConfig = ref([
  {
    id: 1,
    paramsName: 'max_tokens',
    value: 'maxTokens',
    paramsSwitch: false,
    valueRange: '[1,1024]',
    min: 1,
    max: 1024,
    defaultValue: 256,
    minStep: 1,
    error: false,
    parameterDefinition: '模型最大输出token数',
  },
  {
    id: 2,
    paramsName: 'temperature',
    value: 'temperature',
    paramsSwitch: false,
    valueRange: '[0.01,1.00]',
    min: 0.01,
    max: 1,
    defaultValue: 0.95,
    minStep: 0.01,
    error: false,
    parameterDefinition: '控制生成的随机性，较高的值会产生更多样化的输出。建议根据应用场景调整 top_p 或 temperature 参数，但不要同时调整两个参数。',
  },
  {
    id: 3,
    paramsName: 'top_p',
    value: 'topP',
    paramsSwitch: false,
    valueRange: '[0.01,1.00]',
    min: 0.01,
    max: 1,
    defaultValue: 0.01,
    minStep: 0.01,
    error: false,
    parameterDefinition: '控制模型默认参数可能生成过程中考虑的词汇范围，使用累计概率选择候选词，直到累计概率超过给定的阈值。',
  },
  {
    id: 4,
    paramsName: '系统人设',
    value: 'prompt',
    paramsSwitch: false,
    valueRange: '-',
    defaultValue: '你是一个乐于解答各种问题的助手，你的任务是为用户提供专业、准确、有见地的建议',
    minStep: '-',
    error: false,
    parameterDefinition: '非必填。文本输入框，0-1000字符，用户可在此定义prompt。',
  },
]);
let cloneAdjustableParamsConfig = ref([]);
const firstTwoData = ref([
  {
    id: 1,
    paramsName: 'max_tokens',
    value: 'maxTokens',
    paramsSwitch: false,
    valueRange: '[1,1024]',
    min: 1,
    max: 1024,
    defaultValue: 256,
    minStep: 1,
    error: false,
    parameterDefinition: '模型最大输出token数',
  },
  {
    id: 2,
    paramsName: 'temperature',
    value: 'temperature',
    paramsSwitch: false,
    valueRange: '[0.01,1.00]',
    min: 0.01,
    max: 1,
    defaultValue: 0.95,
    minStep: 0.01,
    error: false,
    parameterDefinition: '控制生成的随机性，较高的值会产生更多样化的输出。建议根据应用场景调整 top_p 或 temperature 参数，但不要同时调整两个参数。',
  },
  {
    id: 3,
    paramsName: 'top_p',
    value: 'topP',
    paramsSwitch: false,
    valueRange: '[0.01,1.00]',
    min: 0.01,
    max: 1,
    defaultValue: 0.01,
    minStep: 0.01,
    error: false,
    parameterDefinition: '控制模型默认参数可能生成过程中考虑的词汇范围，使用累计概率选择候选词，直到累计概率超过给定的阈值。',
  },
  {
    id: 4,
    paramsName: '系统人设',
    value: 'prompt',
    paramsSwitch: false,
    valueRange: '-',
    defaultValue: '你是一个乐于解答各种问题的助手，你的任务是为用户提供专业、准确、有见地的建议',
    minStep: '-',
    error: false,
    parameterDefinition: '非必填。文本输入框，0-1000字符，用户可在此定义prompt。',
  },
]);
const cloneFirstTwoData = ref(cloneDeep(firstTwoData));
const thirdData = ref([
  {
    id: 5,
    paramsName: 'step',
    value: 'step',
    paramsSwitch: false,
    valueRange: '[1,100]',
    min: 1,
    max: 100,
    defaultValue: 25,
    minStep: 1,
    error: false,
    parameterDefinition: '前向推理迭代的步数，步数越大，生成图像质量越好，速度越慢。',
  },
]);
const cloneThirdData = ref(cloneDeep(thirdData));
const columns = ref([
  {
    title: '参数名称',
    dataIndex: 'paramsName',
    key: 'paramsName',
    width: 180,
  },
  {
    title: '取值范围',
    dataIndex: 'valueRange',
    key: 'valueRange',
    width: 180,
  },
  {
    title: '默认值',
    dataIndex: 'defaultValue',
    key: 'defaultValue',
    width: 220,
  },
  {
    title: '取值最小间隔',
    dataIndex: 'minStep',
    key: 'minStep',
    width: 120,
  },
  {
    title: '参数释义',
    dataIndex: 'parameterDefinition',
    key: 'parameterDefinition',
    width: 480,
  },
]);
// 校验URL
const onCheckUrl = async (_rule, value) => {
  if (!value.trim()) {
    return Promise.reject('请输入');
  }
  if (!urlregex.test(value)) {
    return Promise.reject('请输入正确的调用服务地址');
  }
  return Promise.resolve();
};
// 模型描述校验相关
const onCheckDesc = async (_rule, value) => {
  if (!value.trim()) {
    return Promise.reject('请输入');
  }
  return Promise.resolve();
};
const requestCode = ref('{<br>"model": "modelname",<br>"messages": [{ <br>&nbsp;&nbsp;&nbsp;&nbsp;"role": "system", <br>&nbsp;&nbsp;&nbsp;&nbsp;"content": "You are a student who is good at math."<br>},<br>{<br>&nbsp;&nbsp;&nbsp;&nbsp;"role": "user", <br>&nbsp;&nbsp;&nbsp;&nbsp;"content": "你是谁？"<br>}<br>],<br>"max_tokens": 256,<br>"temperature": 1.5, <br>"top_p": 0.95, <br>"stream": true <br>}');
const responseCode = ref('{<br>"id": "201",<br>"object": "chat.completion.chunk",<br>"created": 1729238728,<br>"model": "modelname",<br>"choices": [{<br>&nbsp;&nbsp;&nbsp;&nbsp;"index": 0,<br>&nbsp;&nbsp;&nbsp;&nbsp;"delta": {<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"role": "assistant",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"content": "\n\n我是焕新社区人工智能助手?"<br>&nbsp;&nbsp;&nbsp;&nbsp;},<br>&nbsp;&nbsp;&nbsp;&nbsp;"finish_reason": "null"<br>}]<br>}');
const activeKey = ref(['1']);
const tabActiveKey = ref('1');
const customStyle = 'background: rgba(0, 20, 26, 0.02);border-radius: 2px;margin-bottom: 8px;border: 0;overflow: hidden';
// 校验规则
const rules = {
  experienceType: [{ required: true, message: '请选择体验类型' }],
  serviceUrl: [{ required: true, validator: onCheckUrl, trigger: ['change', 'blur'] }],
  appCode: [{ required: true, validator: onCheckDesc, trigger: ['change', 'blur'] }],
};
const typeChange = () => {
  form.value.serviceUrl = '';
  form.value.appCode = '';
};
const changeTabs = (val) => {
  if (val === '0') {
    requestCode.value = '{<br>"model": "modelname",<br>"messages": [{ <br>&nbsp;&nbsp;&nbsp;&nbsp;"role": "system", <br>&nbsp;&nbsp;&nbsp;&nbsp;"content": "You are a student who is good at math."<br>},<br>{<br>&nbsp;&nbsp;&nbsp;&nbsp;"role": "user", <br>&nbsp;&nbsp;&nbsp;&nbsp;"content": "你是谁？"<br>}<br>],<br>"max_tokens": 256,<br>"temperature": 1.5, <br>"top_p": 0.95, <br>"stream": true <br>}';
    responseCode.value = '{<br>"id": "201",<br>"object": "chat.completion.chunk",<br>"created": 1729238728,<br>"model": "modelname",<br>"choices": [{<br>&nbsp;&nbsp;&nbsp;&nbsp;"index": 0,<br>&nbsp;&nbsp;&nbsp;&nbsp;"delta": {<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"role": "assistant",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"content": "\n\n我是焕新社区人工智能助手?"<br>&nbsp;&nbsp;&nbsp;&nbsp;},<br>&nbsp;&nbsp;&nbsp;&nbsp;"finish_reason": "null"<br>}]<br>}';
    if (val === props.defaultData.experienceType) {
      cloneAdjustableParamsConfig.value.forEach((item) => {
        firstTwoData.value.forEach((subItem) => {
          if (item.paramsName === subItem.paramsName) {
            subItem.valueRange = item.valueRange;
            subItem.defaultValue = item.defaultValue;
            subItem.paramsSwitch = item.paramsSwitch;
          }
        });
      });
      adjustableParamsConfig.value = firstTwoData.value;
    } else {
      adjustableParamsConfig.value = cloneFirstTwoData.value;
    }
  } else if (val === '1') {
    requestCode.value = '{ <br>&nbsp;&nbsp;&nbsp;&nbsp;"prompt": "请描述图片内容",<br>&nbsp;&nbsp;&nbsp;&nbsp;"max_tokens": 128,<br>&nbsp;&nbsp;&nbsp;&nbsp;"temperature": 0.7,<br>&nbsp;&nbsp;&nbsp;&nbsp;"image_base64": "xxxxxx"<br>}';
    responseCode.value = '{ <br>"answer": "在这幅温馨的粉色背景图中，一只棕色的泰迪熊显得十分可爱。它的眼睛明亮有神，鼻子鲜红诱人，嘴巴微微张开，似乎在微笑或准备开口。泰迪熊的耳朵竖起，似乎在倾听周围的声音。画面上方有“泰迪熊”字样，下方则有“萌宠”二字，共同营造出一个充满爱意的氛围。快来感受这份纯真的快乐吧！",<br>"code": 20000,<br>"created": 1729238883,<br>"object": "chat",<br>"status": "sucessful response",<br>}';
    if (val === props.defaultData.experienceType) {
      cloneAdjustableParamsConfig.value.forEach((item) => {
        firstTwoData.value.forEach((subItem) => {
          if (item.paramsName === subItem.paramsName) {
            subItem.valueRange = item.valueRange;
            subItem.defaultValue = item.defaultValue;
            subItem.paramsSwitch = item.paramsSwitch;
          }
        });
      });
      adjustableParamsConfig.value = firstTwoData.value;
    } else {
      adjustableParamsConfig.value = cloneFirstTwoData.value;
    }
  } else {
    requestCode.value = '{ <br>&nbsp;&nbsp;&nbsp;&nbsp;"prompt": "生成一张中秋节海报",<br>&nbsp;&nbsp;&nbsp;&nbsp;"step": 25<br>}';
    responseCode.value = '{ <br>&nbsp;&nbsp;&nbsp;&nbsp;"code": 201,<br>&nbsp;&nbsp;&nbsp;&nbsp;"result": {<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"img:"/【base64编码】",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"message": "generate sucess",<br>&nbsp;&nbsp;&nbsp;&nbsp;}<br>}';
    if (val === props.defaultData.experienceType) {
      cloneAdjustableParamsConfig.value.forEach((item) => {
        thirdData.value.forEach((subItem) => {
          if (item.paramsName === subItem.paramsName) {
            subItem.valueRange = item.valueRange;
            subItem.defaultValue = item.defaultValue;
            subItem.paramsSwitch = item.paramsSwitch;
          }
        });
      });
      adjustableParamsConfig.value = thirdData.value;
    } else {
      adjustableParamsConfig.value = cloneThirdData.value;
    }
  }
};
const emits = defineEmits(['is-validate']);
const handleValide = async () => {
  return new Promise((resolve, reject) => {
    formRef.value.validate().then(
      () => {
        let flag = adjustableParamsConfig.value.every((item) => {
          return item.error == false;
        });
        if (flag) {
          isValidate.value = true;
          emits('is-validate', isValidate.value);
          resolve(true);
        } else {
          isValidate.value = false;
          emits('is-validate', isValidate.value);
        }
      },
      (err) => {
        isValidate.value = false;
        emits('is-validate', isValidate.value);
        reject(new Error(false));
      }
    );
  });
};
const valueRangeChange = (record) => {
  // record.defaultValue = 0;
};
const valueRangeBlur = (record) => {
  const firstCode = record.valueRange[0];
  const lastCode = record.valueRange[record.valueRange.length - 1];
  if (record.valueRange !== '') {
    if (firstCode === '[' && lastCode === ']') {
      const firstNum = Number(record.valueRange.substring(1, record.valueRange.indexOf(',')));
      const lastNum = Number(record.valueRange.substring(record.valueRange.indexOf(',') + 1, record.valueRange.length - 1));
      if (!isNumeric(firstNum) || !isNumeric(lastNum)) {
        record.error = true;
        errorText.value = '请输入正确的取值范围';
      } else {
        if (firstNum < 0 || lastNum < 0) {
          record.error = true;
          errorText.value = '请输入正确的取值范围';
        } else {
          record.error = false;
          if (record.paramsName === 'max_tokens') {
            let hasFirstDot = String(firstNum).indexOf('.') !== -1;
            let hasLastDot = String(lastNum).indexOf('.') !== -1;
            if (hasFirstDot || hasLastDot) {
              record.error = true;
              errorText.value = '请输入整数';
            } else {
              record.error = false;
            }
          }
        }
      }
      if (firstCode == '[') {
        record.min = firstNum;
      } else {
        record.min = firstNum + Number(record.minStep);
      }
      if (lastCode === ']') {
        record.max = lastNum;
      } else {
        record.max = lastNum - Number(record.minStep);
      }
      if (Number(record.defaultValue) < Number(record.min) || Number(record.defaultValue) > Number(record.max)) {
        record.defaultValue = record.min;
      }
    } else {
      record.error = true;
      errorText.value = '请使用方括号“[]”来界定取值范围的上下限';
    }
  } else {
    record.error = true;
    errorText.value = '请输入';
  }
};
const clearValidate = () => {
  formRef.value.clearValidate();
};
const integrateData = () => {
  adjustableParamsConfig.value.forEach((item) => {
    item.defaultValue = item.defaultValue.toString();
  });
  return { ...form.value, adjustableParamsConfig };
};
defineExpose({
  validate: handleValide,
  getData: integrateData,
  clearValidate: clearValidate,
});
watch(
  () => props.defaultData,
  (val) => {
    if (!isEmpty(props.defaultData)) {
      form.value.experienceType = val.experienceType || '0';
      form.value.serviceUrl = val.serviceUrl || '';
      form.value.appCode = val.appCode || '';
      adjustableParamsConfig.value = val.adjustableParamsConfig || [];
      cloneAdjustableParamsConfig.value = cloneDeep(adjustableParamsConfig.value);
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
watch(
  () => form,
  (val) => {
    emits('is-validate', true);
  },
  {
    deep: true,
  }
);
watch(
  () => form.value.experienceType,
  (val) => {
    changeTabs(val);
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>

<style lang="less" scoped>
.big-gap {
  margin-bottom: 32px;
}
.exp-collapse.ant-collapse {
  width: 650px;
  background: #fafafb;
  margin-top: 12px;
}
.exp-collapse .ant-collapse-header-text {
  span {
    font-weight: 400;
    font-size: 14px;
    color: #00141a;
  }
  .type {
    margin-left: 16px;
    color: rgba(0, 20, 26, 0.7);
  }
}
.coll-text {
  font-size: 14px;
  color: #00a0cc;
  margin-right: 10px;
}
.params-name {
  display: inline-block;
  width: 80px;
}
.error-text {
  color: #fe3a47;
}
.flex-item {
  margin-top: 21px;
}
.request-item {
  font-family: PingFangSC, PingFang SC, sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #00141a;
}
:deep(.ant-tabs .ant-tabs-tab:hover) {
  color: #6d787c;
}
:deep(.ant-tabs-nav .ant-tabs-tab-active .tab-point) {
  background: #00a0cc;
}
:deep(.ant-checkbox-group .ant-checkbox-wrapper) {
  align-items: center;
}
:deep(.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab) {
  border-radius: 0;
  padding: 3px 126px;
  background-color: #fff;
  .ant-tabs-tab-btn {
    font-size: 12px;
  }
}
:deep(.ant-tabs-top > .ant-tabs-nav::before) {
  border-bottom: 0;
}

:deep(.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab-active) {
  border-color: @jt-primary-color !important;
}
:deep(.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab:nth-child(2)) {
  margin-left: 0;
  border-left-color: transparent;
}
</style>
