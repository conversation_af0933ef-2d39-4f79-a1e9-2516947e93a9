<template>
  <div class="operate-box">
    <a-alert :message="generateAlertMessage()" style="margin-bottom: 20px" type="info" show-icon />
    <a-collapse v-if="checkResourceAuth('postPretrain')" v-model:activeKey="activeKey" :bordered="false" collapsible="icon" expand-icon-position="end">
      <template #expandIcon="{ isActive }">
        <span class="icon-box">
          <jt-icon type="iconshouqi" class="iconfont" :rotate="isActive ? 0 : -180" />
        </span>
      </template>
      <a-collapse-panel key="1" :class="{ isError: !isIncrementPreTrainVal }" :style="customStyle">
        <template #header>
          <span class="coll-flex">
            <span>
              <span style="margin: 8px 0">增量预训练配置</span>
            </span>
            <a-switch v-model:checked="configPar.incrementPreTrain" checked-children="开" un-checked-children="关" @change="preTrainSwitchChange" />
            <span v-if="!isIncrementPreTrainVal" class="tip-text">请填写完整无误</span>
          </span>
        </template>
        <increment-pre-train ref="incrementPreTrainRef" :default-data="incrementPreTrainData" :data-set-data="dataSetData" :is-check="configPar.incrementPreTrain" :data-set-type="props.dataSetType" @is-validate="incrementPreTrainVal" @incrementChange="incrementChange"></increment-pre-train>
      </a-collapse-panel>
    </a-collapse>
    <a-collapse v-if="checkResourceAuth('supervisedFinetune')" v-model:activeKey="activeKey" :bordered="false" collapsible="icon" expand-icon-position="end">
      <template #expandIcon="{ isActive }">
        <span class="icon-box">
          <jt-icon type="iconshouqi" class="iconfont" :rotate="isActive ? 0 : -180" />
        </span>
      </template>
      <a-collapse-panel key="2" :class="{ isError: !isSuperviseFinetuningVal }" :style="customStyle">
        <template #header>
          <span class="coll-flex">
            <span>
              <span style="margin: 8px 0">有监督微调配置</span>
            </span>
            <a-switch v-model:checked="configPar.superviseFinetuning" checked-children="开" un-checked-children="关" @change="finetuningSwitchChange" />
            <span v-if="!isSuperviseFinetuningVal" class="tip-text">请填写完整无误</span>
          </span>
        </template>
        <supervise-finetuning ref="superviseFinetuningRef" :default-data="superviseFinetuningData" :data-set-data="dataSetData" :is-check="configPar.superviseFinetuning" :data-set-type="props.dataSetType" @is-validate="superviseFinetuningVal" @superviseChange="superviseChange"></supervise-finetuning>
      </a-collapse-panel>
    </a-collapse>
    <a-collapse v-if="checkResourceAuth('preferAlign')" v-model:activeKey="activeKey" :bordered="false" collapsible="icon" expand-icon-position="end">
      <template #expandIcon="{ isActive }">
        <span class="icon-box">
          <jt-icon type="iconshouqi" class="iconfont" :rotate="isActive ? 0 : -180" />
        </span>
      </template>
      <a-collapse-panel key="3" :class="{ isError: !isHobbyAlineVal }" :style="customStyle">
        <template #header>
          <span class="coll-flex">
            <span>
              <span style="margin: 8px 0">偏好对齐</span>
            </span>
            <a-switch v-model:checked="configPar.hobbyAline" checked-children="开" un-checked-children="关" @change="hobbySwitchChange" />
            <span v-if="!isHobbyAlineVal" class="tip-text">请填写完整无误</span>
          </span>
        </template>
        <hobby-aline ref="hobbyAlineRef" :default-data="hobbyAlineData" :is-check="configPar.hobbyAline" :data-set-data="dataSetData" :data-set-type="props.dataSetType" @is-validate="hobbyAlineVal" @hobbyChange="hobbyChange"></hobby-aline>
      </a-collapse-panel>
    </a-collapse>
    <a-collapse v-if="checkResourceAuth('modelCompress')" v-model:activeKey="activeKey" :bordered="false" collapsible="icon" expand-icon-position="end">
      <template #expandIcon="{ isActive }">
        <span class="icon-box">
          <jt-icon type="iconshouqi" class="iconfont" :rotate="isActive ? 0 : -180" />
        </span>
      </template>
      <a-collapse-panel key="4" :class="{ isError: !isModelCompressionVal }" :style="customStyle">
        <template #header>
          <span class="coll-flex">
            <span>
              <span style="margin: 8px 0">模型压缩</span>
            </span>
            <a-switch v-model:checked="configPar.modelCompression" checked-children="开" un-checked-children="关" @change="compressSwitchChange" />
            <span v-if="!isModelCompressionVal" class="tip-text">请填写完整无误</span>
          </span>
        </template>
        <model-compression ref="modelCompressionRef" :default-data="modelCompressionData" :is-check="configPar.modelCompression" @is-validate="modelCompressionVal" @strategyChange="strategyChange"></model-compression>
      </a-collapse-panel>
    </a-collapse>
    <a-collapse v-if="checkResourceAuth('modelEvaluation')" v-model:activeKey="activeKey" :bordered="false" collapsible="icon" expand-icon-position="end">
      <template #expandIcon="{ isActive }">
        <span class="icon-box">
          <jt-icon type="iconshouqi" class="iconfont" :rotate="isActive ? 0 : -180" />
        </span>
      </template>
      <a-collapse-panel key="5" :class="{ isError: !isModelEvaluationVal }" :style="customStyle">
        <template #header>
          <span class="coll-flex">
            <span>
              <span style="margin: 8px 0">模型评估</span>
            </span>
            <a-switch v-model:checked="configPar.modelEvaluation" checked-children="开" un-checked-children="关" @change="evaluateSwitchChange" />
            <span v-if="!isModelEvaluationVal" class="tip-text">请填写完整无误</span>
          </span>
        </template>
        <model-evaluation ref="modelEvaluationRef" :default-data="modelEvaluationData" :is-check="configPar.modelEvaluation" :is-com-open="configPar.modelCompression" :is-hobby-aline="configPar.hobbyAline" :is-supervise="configPar.superviseFinetuning" :is-increment="configPar.incrementPreTrain" :compress-data="strategyList" :increment-data="incrementList" :supervise-data="superviseList" :hobby-data="hobbyList" :data-set-type="props.dataSetType" @is-validate="modelEvaluationVal"></model-evaluation>
      </a-collapse-panel>
    </a-collapse>
    <a-collapse v-if="checkResourceAuth('inferService')" v-model:activeKey="activeKey" :bordered="false" collapsible="icon" expand-icon-position="end">
      <template #expandIcon="{ isActive }">
        <span class="icon-box">
          <jt-icon type="iconshouqi" class="iconfont" :rotate="isActive ? 0 : -180" />
        </span>
      </template>
      <a-collapse-panel key="6" :class="{ isError: !isModelReasoningVal }" :style="customStyle">
        <template #header>
          <span class="coll-flex">
            <span>
              <span style="margin: 8px 0">模型推理</span>
            </span>
            <a-switch v-model:checked="configPar.modelReasoning" checked-children="开" un-checked-children="关" @change="reasonSwitchChange" />
            <span v-if="!isModelReasoningVal" class="tip-text">请填写完整无误</span>
          </span>
        </template>
        <model-reasoning ref="modelReasoningRef" :is-com-open="configPar.modelCompression" :is-hobby-aline="configPar.hobbyAline" :is-supervise="configPar.superviseFinetuning" :is-increment="configPar.incrementPreTrain" :compress-data="strategyList" :increment-data="incrementList" :supervise-data="superviseList" :hobby-data="hobbyList" :default-data="modelReasoningData" :is-check="configPar.modelReasoning" @is-validate="modelReasoningVal"></model-reasoning>
      </a-collapse-panel>
    </a-collapse>
    <a-collapse v-if="checkResourceAuth('modelExperience')" v-model:activeKey="activeKey" :bordered="false" collapsible="icon" expand-icon-position="end">
      <template #expandIcon="{ isActive }">
        <span class="icon-box">
          <jt-icon type="iconshouqi" class="iconfont" :rotate="isActive ? 0 : -180" />
        </span>
      </template>
      <a-collapse-panel key="7" :class="{ isError: !isModelExperienceVal }" :style="customStyle">
        <template #header>
          <span class="coll-flex">
            <span>
              <span style="margin: 8px 0">模型体验</span>
            </span>
            <a-switch v-model:checked="configPar.modelExperience" checked-children="开" un-checked-children="关" @change="experienceSwitchChange" />
            <span v-if="!isModelExperienceVal" class="tip-text">请填写完整无误</span>
          </span>
        </template>
        <model-experience ref="modelExperienceRef" :default-data="modelExperienceData" :is-check="configPar.modelExperience" @is-validate="modelExperienceVal"></model-experience>
      </a-collapse-panel>
    </a-collapse>
    <a-collapse v-if="checkResourceAuth('inferService')" v-model:activeKey="activeKey" :bordered="false" collapsible="icon" expand-icon-position="end">
      <template #expandIcon="{ isActive }">
        <span class="icon-box">
          <jt-icon type="iconshouqi" class="iconfont" :rotate="isActive ? 0 : -180" />
        </span>
      </template>
      <a-collapse-panel key="8" :class="{ isError: !isApiCallingVal }" :style="customStyle">
        <template #header>
          <span class="coll-flex">
            <span>
              <span style="margin: 8px 0">API调用</span>
            </span>
            <a-switch v-model:checked="configPar.apiCalling" checked-children="开" un-checked-children="关" @change="apiSwitchChange" />
            <span v-if="!isApiCallingVal" class="tip-text">请填写完整无误</span>
          </span>
        </template>
        <api-calling ref="apiCallingRef" :default-data="apiCallingData" :is-check="configPar.apiCalling" @is-validate="apiCallingVal"></api-calling>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<script setup>
import isEmpty from 'lodash/isEmpty';
import incrementPreTrain from './incrementPreTrain';
import superviseFinetuning from './superviseFinetuning';
import hobbyAline from './hobbyAline';
import modelCompression from './modelCompression';
import modelEvaluation from './modelEvaluation';
import modelReasoning from './modelReasoning';
import modelExperience from './modelExperience';
import apiCalling from './apiCalling';
import { presetModelApi } from '@/apis';
import { checkResourceAuth } from '@/utils/auth';
const route = useRoute();
const props = defineProps({
  baseData: {
    type: Object,
    default: () => {},
  },
  fileData: {
    type: Object,
    default: () => {},
  },
  introduceData: {
    type: Object,
    default: () => {},
  },
  configData: {
    type: Object,
    default: () => {},
  },
  dataSetType: {
    type: Object,
    default: () => {},
  },
});
import { useStore } from 'vuex';
import { message } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
const router = useRouter();

const store = useStore();
const poolId = store.state.poolInfo.id;
const activeKey = ref(['1']);
const customStyle = 'background: #FFFFFF;border-radius: 2px;border: 1px solid rgba(0,20,26,0.15);margin-bottom:20px';
const incrementList = ref([]);
const strategyList = ref([]);
const superviseList = ref([]);
const hobbyList = ref([]);
const dataSetData = ref([]);
let isIncrementPreTrainVal = ref(true);
let isSuperviseFinetuningVal = ref(true);
let isHobbyAlineVal = ref(true);
let isModelCompressionVal = ref(true);
let isModelEvaluationVal = ref(true);
let isModelReasoningVal = ref(true);
let isModelExperienceVal = ref(true);
let isApiCallingVal = ref(true);
const incrementPreTrainRef = ref(null);
const superviseFinetuningRef = ref(null);
const hobbyAlineRef = ref(null);
const modelCompressionRef = ref(null);
const modelEvaluationRef = ref(null);
const modelReasoningRef = ref(null);
const modelExperienceRef = ref(null);
const apiCallingRef = ref(null);
const incrementPreTrainData = ref({});
const superviseFinetuningData = ref({});
const hobbyAlineData = ref({});
const modelCompressionData = ref({});
const modelEvaluationData = ref({});
const modelReasoningData = ref({});
const modelExperienceData = ref({});
const apiCallingData = ref({});
const configPar = ref({
  incrementPreTrain: checkResourceAuth('postPretrain'),
  superviseFinetuning: false,
  hobbyAline: false,
  modelCompression: false,
  modelEvaluation: false,
  modelReasoning: false,
  modelExperience: false,
  apiCalling: false,
});
const strategyChange = (val) => {
  strategyList.value = val;
};
const incrementChange = (val) => {
  incrementList.value = val;
};
const superviseChange = (val) => {
  superviseList.value = val;
};
const hobbyChange = (val) => {
  hobbyList.value = val;
};
const handleValide = () => {
  const arr = [];
  const validateList = [];
  for (let i in configPar.value) {
    if (configPar.value[i]) {
      arr.push(i);
    }
  }

  arr.forEach((item) => {
    if (item === 'incrementPreTrain') {
      if (incrementPreTrainRef.value === null) {
        isIncrementPreTrainVal.value = false;
      } else {
        validateList.push(incrementPreTrainRef.value.validate());
      }
    } else if (item === 'superviseFinetuning') {
      if (superviseFinetuningRef.value === null) {
        isSuperviseFinetuningVal.value = false;
      } else {
        validateList.push(superviseFinetuningRef.value.validate());
      }
    } else if (item === 'hobbyAline') {
      if (hobbyAlineRef.value === null) {
        isHobbyAlineVal.value = false;
      } else {
        validateList.push(hobbyAlineRef.value.validate());
      }
    } else if (item === 'modelCompression') {
      if (modelCompressionRef.value === null) {
        isModelCompressionVal.value = false;
      } else {
        validateList.push(modelCompressionRef.value.validate());
      }
    } else if (item === 'modelEvaluation') {
      if (modelEvaluationRef.value === null) {
        isModelEvaluationVal.value = false;
      } else {
        validateList.push(modelEvaluationRef.value.validate());
      }
    } else if (item === 'modelReasoning') {
      if (modelReasoningRef.value === null) {
        isModelReasoningVal.value = false;
      } else {
        validateList.push(modelReasoningRef.value.validate());
      }
    } else if (item === 'modelExperience') {
      if (modelExperienceRef.value === null) {
        isModelExperienceVal.value = false;
      } else {
        validateList.push(modelExperienceRef.value.validate());
      }
    } else if (item === 'apiCalling') {
      if (apiCallingRef.value === null) {
        isApiCallingVal.value = false;
      } else {
        validateList.push(apiCallingRef.value.validate());
      }
    }
  });
  Promise.all(validateList)
    .then((res) => {
      incrementPreTrainData.value = incrementPreTrainRef.value ? incrementPreTrainRef.value.getData() : {};
      superviseFinetuningData.value = superviseFinetuningRef.value ? superviseFinetuningRef.value.getData() : {};
      hobbyAlineData.value = hobbyAlineRef.value ? hobbyAlineRef.value.getData() : {};
      modelCompressionData.value = modelCompressionRef.value ? modelCompressionRef.value.getData() : {};
      modelEvaluationData.value = modelEvaluationRef.value ? modelEvaluationRef.value.getData() : {};
      modelReasoningData.value = modelReasoningRef.value ? modelReasoningRef.value.getData() : {};
      modelExperienceData.value = modelExperienceRef.value ? modelExperienceRef.value.getData() : {};
      apiCallingData.value = apiCallingRef.value ? apiCallingRef.value.getData() : {};
      if (isIncrementPreTrainVal.value && isSuperviseFinetuningVal.value && isHobbyAlineVal.value && isModelCompressionVal.value && isModelEvaluationVal.value && isModelReasoningVal.value && isModelExperienceVal.value && isApiCallingVal.value) {
        const params = {
          poolId,
          name: props.baseData.name,
          category: props.baseData.category,
          label: props.baseData.label,
          framework: props.baseData.framework,
          network: props.baseData.network,
          description: props.baseData.description,
          intro: props.introduceData.intro,
          openSource: props.fileData.openSource,
          fileFormat: props.fileData.fileFormat,
          customModelId: props.fileData.customModelId,
          createdBy: store.state.userInfo.userName,
          config: {
            pretrainingConfig: {
              addToOperateList: configPar.value.incrementPreTrain,
              ...incrementPreTrainData.value,
            },
            sftConfig: { ...superviseFinetuningData.value, addToOperateList: configPar.value.superviseFinetuning },
            preferenceConfig: { ...hobbyAlineData.value, addToOperateList: configPar.value.hobbyAline },
            compressConfig: { ...modelCompressionData.value, addToOperateList: configPar.value.modelCompression },
            evaluateConfig: { ...modelEvaluationData.value, addToOperateList: configPar.value.modelEvaluation },
            servingConfig: { ...modelReasoningData.value, addToOperateList: configPar.value.modelReasoning },
            experienceConfig: { ...modelExperienceData.value, addToOperateList: configPar.value.modelExperience },
            apiConfig: { ...apiCallingData.value, addToOperateList: configPar.value.apiCalling },
          },
        };
        if (params.config.pretrainingConfig.validationSetType && Array.isArray(params.config.pretrainingConfig.validationSetType)) {
          params.config.pretrainingConfig.validationSetType = params.config.pretrainingConfig.validationSetType.join(',');
        }
        if (params.config.sftConfig.validationSetType && Array.isArray(params.config.sftConfig.validationSetType)) {
          params.config.sftConfig.validationSetType = params.config.sftConfig.validationSetType.join(',');
        }
        if (params.config.preferenceConfig.validationSetType && Array.isArray(params.config.preferenceConfig.validationSetType)) {
          params.config.preferenceConfig.validationSetType = params.config.preferenceConfig.validationSetType.join(',');
        }
        if (params.config.compressConfig.strategy && Array.isArray(params.config.compressConfig.strategy)) {
          params.config.compressConfig.strategy = params.config.compressConfig.strategy.join(',');
        }
        if (params.config.evaluateConfig.evaluateMethod && Array.isArray(params.config.evaluateConfig.evaluateMethod)) {
          params.config.evaluateConfig.evaluateMethod = params.config.evaluateConfig.evaluateMethod.join(',');
        }
        if (params.config.evaluateConfig.evaluateScoringRule && Array.isArray(params.config.evaluateConfig.evaluateScoringRule)) {
          params.config.evaluateConfig.evaluateScoringRule = params.config.evaluateConfig.evaluateScoringRule.join(',');
        }
        if (params.config.evaluateConfig.evaluationMethod && Array.isArray(params.config.evaluateConfig.evaluationMethod)) {
          params.config.evaluateConfig.evaluationMethod = params.config.evaluateConfig.evaluationMethod.join(',');
        }
        if (params.config.sftConfig.trainingMethods && Array.isArray(params.config.sftConfig.trainingMethods)) {
          params.config.sftConfig.trainingMethods = params.config.sftConfig.trainingMethods.join(',');
        }
        if (params.config.apiConfig.callMethod && Array.isArray(params.config.apiConfig.callMethod)) {
          params.config.apiConfig.callMethod = params.config.apiConfig.callMethod.join(',');
        }
        if (params.config.evaluateConfig?.evaluateDatasetType?.length) {
          params.config.evaluateConfig.evaluateDatasetType = params.config.evaluateConfig.evaluateDatasetType.map((item) => item);
        }
        let fnName = route.query.id ? 'editPreModel' : 'createPreModel';
        if (route.query.id) {
          params.id = parseInt(route.query.id);
        }
        console.log('params', params);
        presetModelApi[fnName](params)
          .then((res) => {
            if (res.code === 0 || res.code === 180000) {
              router.push({ name: 'preset-asset' });
              message.success(`模型${route.query.id ? '编辑' : '新建'}成功`);
            }
          })
          .catch((err) => {
            throw new Error(err);
          });
      }
    })
    .catch((err) => {
      throw new Error(err);
    });
};
const integrateData = () => {
  let iprConfigData = incrementPreTrainRef.value ? { ...incrementPreTrainRef.value.getData() } : {};
  let sftConfigData = superviseFinetuningRef.value ? { ...superviseFinetuningRef.value.getData() } : {};
  let preferenceConfigData = hobbyAlineRef.value ? { ...hobbyAlineRef.value.getData() } : {};
  let compressConfigData = modelCompressionRef.value ? { ...modelCompressionRef.value.getData() } : {};
  let evaluateConfigData = modelEvaluationRef.value ? { ...modelEvaluationRef.value.getData() } : {};
  let servingConfigData = modelReasoningRef.value ? { ...modelReasoningRef.value.getData() } : {};
  let experienceConfigData = modelExperienceRef.value ? { ...modelExperienceRef.value.getData() } : {};
  let apiConfigData = apiCallingRef.value ? { ...apiCallingRef.value.getData() } : {};
  return {
    config: {
      pretrainingConfig: { iprConfigData, addToOperateList: configPar.value.incrementPreTrain },
      sftConfig: { sftConfigData, addToOperateList: configPar.value.superviseFinetuning },
      preferenceConfig: { preferenceConfigData, addToOperateList: configPar.value.hobbyAline },
      compressConfig: { compressConfigData, addToOperateList: configPar.value.modelCompression },
      evaluateConfig: { evaluateConfigData, addToOperateList: configPar.value.modelEvaluation },
      servingConfig: { servingConfigData, addToOperateList: configPar.value.modelReasoning },
      experienceConfig: { experienceConfigData, addToOperateList: configPar.value.modelExperience },
      apiConfig: { apiConfigData, addToOperateList: configPar.value.apiCalling },
    },
  };
};
defineExpose({
  getData: integrateData,
  validate: handleValide,
});
const generateAlertMessage = () => {
  const modules = [];
  if (checkResourceAuth('postPretrain')) modules.push('增量预训练');
  if (checkResourceAuth('supervisedFinetune')) modules.push('有监督微调');
  if (checkResourceAuth('preferAlign')) modules.push('偏好对齐');
  // if (checkResourceAuth('knowledgeDistillation')) modules.push('模型蒸馏');
  if (checkResourceAuth('modelCompress')) modules.push('模型压缩');
  if (checkResourceAuth('modelEvaluation')) modules.push('模型评估');
  if (checkResourceAuth('inferService')) modules.push('在线服务');
  if (checkResourceAuth('modelExperience')) modules.push('模型体验');
  if (checkResourceAuth('inferService')) modules.push('API调用');

  if (modules.length === 0) {
    return '暂无可用的功能模块进行操作配置';
  }

  return `可对${modules.join('、')}等${modules.length}个功能模块进行操作配置，开启配置开关，用户侧可使用此功能`;
};
const preTrainSwitchChange = (val) => {
  if (!val) {
    if (incrementPreTrainRef.value !== null) {
      incrementPreTrainRef.value.clearValidate();
    }
    isIncrementPreTrainVal.value = true;
  }
};
const finetuningSwitchChange = (val) => {
  if (!val) {
    isSuperviseFinetuningVal.value = true;
    if (superviseFinetuningRef.value !== null) {
      superviseFinetuningRef.value.clearValidate();
    }
  }
};
const hobbySwitchChange = (val) => {
  if (!val) {
    if (hobbyAlineRef.value !== null) {
      hobbyAlineRef.value.clearValidate();
    }
    isHobbyAlineVal.value = true;
  }
};
const compressSwitchChange = (val) => {
  if (!val) {
    if (modelCompressionRef.value !== null) {
      modelCompressionRef.value.clearValidate();
    }
    isModelCompressionVal.value = true;
  }
};
const evaluateSwitchChange = (val) => {
  if (!val) {
    if (modelEvaluationRef.value !== null) {
      modelEvaluationRef.value.clearValidate();
    }
    isModelEvaluationVal.value = true;
  }
};
const reasonSwitchChange = (val) => {
  if (!val) {
    if (modelReasoningRef.value !== null) {
      isModelReasoningVal.value = false;
    }
    isModelReasoningVal.value = true;
  }
};
const experienceSwitchChange = (val) => {
  if (!val) {
    if (modelExperienceRef.value !== null) {
      modelExperienceRef.value.clearValidate();
    }
    isModelExperienceVal.value = true;
  }
};
const apiSwitchChange = (val) => {
  if (!val) {
    if (apiCallingRef.value !== null) {
      apiCallingRef.value.clearValidate();
    }
    isApiCallingVal.value = true;
  }
};
const incrementPreTrainVal = (val) => {
  isIncrementPreTrainVal.value = val;
};
const superviseFinetuningVal = (val) => {
  isSuperviseFinetuningVal.value = val;
};
const hobbyAlineVal = (val) => {
  isHobbyAlineVal.value = val;
};
const modelCompressionVal = (val) => {
  isModelCompressionVal.value = val;
};
const modelEvaluationVal = (val) => {
  isModelEvaluationVal.value = val;
};
const modelReasoningVal = (val) => {
  isModelReasoningVal.value = val;
};
const modelExperienceVal = (val) => {
  isModelExperienceVal.value = val;
};
const apiCallingVal = (val) => {
  isApiCallingVal.value = val;
};
watch(
  () => props.configData,
  (val) => {
    if (!isEmpty(props.configData)) {
      if (route.query.id) {
        activeKey.value = ['1', '2', '3', '4', '5', '6', '7', '8'];
      }
      incrementPreTrainData.value = val.config.pretrainingConfig;
      superviseFinetuningData.value = val.config.sftConfig.sftConfigData ? val.config.sftConfig.sftConfigData : val.config.sftConfig;
      hobbyAlineData.value = val.config.preferenceConfig.preferenceConfigData ? val.config.preferenceConfig.preferenceConfigData : val.config.preferenceConfig;
      modelCompressionData.value = val.config.compressConfig.compressConfigData ? val.config.compressConfig.compressConfigData : val.config.compressConfig;
      modelEvaluationData.value = val.config.evaluateConfig.evaluateConfigData ? val.config.evaluateConfig.evaluateConfigData : val.config.evaluateConfig;
      modelReasoningData.value = val.config.servingConfig.servingConfigData ? val.config.servingConfig.servingConfigData : val.config.servingConfig;
      modelExperienceData.value = val.config.experienceConfig.experienceConfigData ? val.config.experienceConfig.experienceConfigData : val.config.experienceConfig;
      apiCallingData.value = val.config.apiConfig.apiConfigData ? val.config.apiConfig.apiConfigData : val.config.apiConfig;
      configPar.value.incrementPreTrain = checkResourceAuth('postPretrain') && val.config.pretrainingConfig.addToOperateList;
      configPar.value.superviseFinetuning = val.config.sftConfig.addToOperateList;
      configPar.value.hobbyAline = val.config.preferenceConfig.addToOperateList;
      configPar.value.modelCompression = val.config.compressConfig.addToOperateList;
      configPar.value.modelEvaluation = val.config.evaluateConfig.addToOperateList;
      configPar.value.modelReasoning = val.config.servingConfig.addToOperateList;
      configPar.value.modelExperience = val.config.experienceConfig.addToOperateList;
      configPar.value.apiCalling = val.config.apiConfig.addToOperateList;
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>

<style lang="less" scoped>
.isError.ant-collapse-item {
  border: 1px solid #fe3a47 !important;
}
.coll-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  .tip-text {
    position: absolute;
    top: 0px;
    right: 56px;
    font-weight: 400;
    font-size: 14px;
    color: #fe3a47;
  }
}
.icon-box {
  display: inline-block;
  width: 24px;
  height: 24px;
  border-radius: 2px;
  border: 1px solid rgba(0, 20, 26, 0.15);
  text-align: center;
  padding-left: 3px;
}
:deep(.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-header-text) {
  font-weight: 700;
  font-size: 14px;
  color: #00141a;
}
:deep(.ant-form-item .ant-form-item-label > label) {
  font-weight: 400;
  font-size: 14px;
  color: #00141a;
}
:deep(.ant-checkbox-group .ant-checkbox-wrapper) {
  align-items: center;
}
:deep(.err-star) {
  color: #fe3a47;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  margin-right: 4px;
  margin-top: 2px;
}
:deep(.ant-form-inline) {
  display: block;
}
:deep(.ant-form-inline .ant-form-item) {
  margin-inline-end: 0;
}
:deep(.left-select .ant-select-selector) {
  border-top-left-radius: 2px;
  border-top-right-radius: 0px;
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 0px;
}
:deep(.right-select .ant-select-selector) {
  border-top-left-radius: 0px;
  border-top-right-radius: 2px;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 2px;
}
:deep(.left-select.ant-input) {
  border-top-left-radius: 2px;
  border-top-right-radius: 0px;
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 0px;
}
:deep(.right-select.ant-input-number) {
  border-radius: 0px;
}
:deep(.ant-input-number-group .ant-input-number-group-addon) {
  border-radius: 2px;
}
:deep(.tran-form .ant-form-item) {
  margin-bottom: 0;
}
:deep(.ant-collapse-item .ant-collapse-header) {
  padding: 16px 20px;
}
</style>
