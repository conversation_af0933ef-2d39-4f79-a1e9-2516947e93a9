<template>
  <a-drawer v-model:open="open" title="处理详情" width="618" :body-style="{ padding: 0 }" class="quota-record-item-modal" @close="handleClose">
    <div class="content">
      <a-descriptions :label-style="{ width: '84px', display: 'inline-block', 'text-align': 'right' }">
        <a-descriptions-item label="处理人" :span="3">{{ operationItem.updateUser }}</a-descriptions-item>
        <a-descriptions-item label="处理时间" :span="3">{{ getFormatTime(operationItem.updateTime) }}</a-descriptions-item>
        <a-descriptions-item label="处理说明" :span="3">{{ operationItem.updateDesc || '--' }}</a-descriptions-item>
        <a-descriptions-item v-if="showEmail" label="邮件通知" :span="3">
          <a-space>
            <span class="send-time">{{ operationItem.emailSendStatus === 0 ? getFormatTime(operationItem.emailSendTime) : '--' }}</span>
            <a-button v-if="editAuth" :disabled="operationItem.emailSendStatus === 4" :loading="emailLoading" type="link" @click="() => resendNotification('email')">重新发送</a-button>
          </a-space>
        </a-descriptions-item>
        <a-descriptions-item v-if="showSMS" label="短信通知" :span="3">
          <a-space>
            <span class="send-time">{{ operationItem.smsSendStatus === 0 ? getFormatTime(operationItem.smsSendTime) : '--' }}</span>
            <a-button v-if="editAuth" :disabled="operationItem.smsSendStatus === 4" :loading="smsLoading" type="link" @click="() => resendNotification('sms')">重新发送</a-button>
          </a-space>
        </a-descriptions-item>
        <a-descriptions-item label="站内信通知" :span="3">
          <a-space>
            <span class="send-time">{{ operationItem.messageSendStatus === 0 ? getFormatTime(operationItem.messageSendTime) : '--' }}</span>
            <a-button v-if="editAuth" :loading="messageLoading" type="link" @click="() => resendNotification('message')">重新发送</a-button>
          </a-space>
        </a-descriptions-item>
        <a-descriptions-item label="变更内容" :span="3">{{ changecontent }}</a-descriptions-item>
        <a-descriptions-item label="申请人" :span="3">{{ operationItem.ticketId ? operationItem.reqUser : '--' }}</a-descriptions-item>
        <a-descriptions-item label="申请时间" :span="3">{{ operationItem.ticketId ? getFormatTime(operationItem.createTime) : '--' }}</a-descriptions-item>
        <a-descriptions-item label="需求名称" :span="3">{{ operationItem.ticketId ? operationItem.reqTitle : '--' }}</a-descriptions-item>
        <a-descriptions-item label="需求描述" :span="3">{{ operationItem.ticketId ? operationItem.reqDesc : '--' }}</a-descriptions-item>
      </a-descriptions>
    </div>
  </a-drawer>
</template>

<script setup>
import { quotaApi } from '@/apis';
import { message } from 'ant-design-vue';
import { getFormatTime } from '@/utils';
import { RESEND_MESSAGE_MAP } from '@/constants/quota.js';
import { showSMS, showEmail } from '@/config';

const props = defineProps({
  id: { type: String, default: '' },
  idType: { type: String, default: 'id' },
  editAuth: { type: Boolean, default: false },
});
const open = defineModel('open', { required: true, default: false });
const emailLoading = ref(false);
const smsLoading = ref(false);
const messageLoading = ref(false);
const operationItem = ref({});

watch(
  () => open.value,
  () => {
    if (open.value) {
      getDetail();
    } else {
      operationItem.value = {};
    }
  }
);

const changecontent = computed(() => {
  if (operationItem.value.curQuota) {
    return `新建项目配额从${operationItem.value.curQuota.crprj}个调整至${operationItem.value.apvQuota.crprj}个`;
  } else {
    return '--';
  }
});

const getDetail = async () => {
  const res = await quotaApi.getQuotaChangeDetail({ [props.idType]: props.id });
  if (res.code === 0) {
    operationItem.value = res.data;
  }
};

const resendNotification = async (notify) => {
  if (notify === 'email') {
    emailLoading.value = true;
  } else if (notify === 'sms') {
    smsLoading.value = true;
  } else if (notify === 'message') {
    messageLoading.value = true;
  }
  const res = await quotaApi.reNotifyQuotaChange({
    id: operationItem.value.id,
    notify,
  });
  if (res.code === 0 && res.data) {
    getDetail();
    message.success('重新发送成功');
  } else {
    message.error(RESEND_MESSAGE_MAP[res.code] || '重新发送失败');
  }
  if (notify === 'email') {
    emailLoading.value = false;
  } else if (notify === 'sms') {
    smsLoading.value = false;
  } else if (notify === 'message') {
    messageLoading.value = false;
  }
};
</script>

<style lang="less" scoped>
.quota-record-item-modal {
  .send-time {
    display: inline-block;
    width: 146px;
  }
  .ant-btn {
    padding: 0px 0px 0px 8px;
    height: auto;
  }
  .content {
    padding: 16px 24px 24px;
  }
  :deep .ant-drawer .ant-drawer-body {
    padding-top: 16px !important;
  }
}
</style>
