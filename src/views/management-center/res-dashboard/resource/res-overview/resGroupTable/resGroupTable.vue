<template>
  <div>
    <div class="overview-title">资源组列表</div>
    <a-config-provider>
      <template #renderEmpty>
        <empty-data title="目前暂无相关资源组"></empty-data>
      </template>
      <a-table :columns="columnsTableCom" :data-source="tableData" :pagination="false" :scroll="{ x: 2000 }" :loading="loading" :get-popup-container="(triggerNode) => triggerNode.parentNode" @change="handleChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'groupName'">
            <div class="group-name">
              <jt-tag rounded="small" :color="record.attribute === 'public' ? 'blue' : 'orange'">{{ record.attribute === 'public' ? `公共` : '专属' }}</jt-tag>
              <a-tooltip placement="top" :title="record.groupName">
                <span class="resource-name" @click="() => toDetail(record)">{{ record.groupName }}</span>
              </a-tooltip>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'scene'">
            <jt-tag v-for="sceneKey in record.scene" :key="sceneKey" rounded="small" :color="SCENARIO_TYPE_TAG_COLOR[sceneKey]">{{ SCENARIO_TYPE[sceneKey] || '--' }}</jt-tag>
          </template>
        </template>
      </a-table>
    </a-config-provider>
    <jt-pagination :total="paginationOptions.total" :page-num="paginationOptions.pageNum" :page-size="paginationOptions.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { columns } from './columns';
import emptyData from '@/components/emptyData';

import { SCENARIO_TYPE, SCENARIO_TYPE_MAP, SCENARIO_TYPE_TAG_COLOR } from '@/constants/nodeList.js';
import { getGpuTypeApi, getClusterApi, getResViewTableApi } from '@/apis/board';
import { checkResourceAuth } from '@/utils/auth';
// 模型调优-增量预训练/有监督微调/偏好对齐/模型压缩/模型评估
const isCheckAuth = checkResourceAuth(['postPretrain', 'supervisedFinetune', 'preferAlign', 'modelCompress', 'modelEvaluation']);

const router = useRouter();

const paginationOptions = ref({
  total: 0,
  pageSize: 10,
  pageNum: 1,
});
const loading = ref(false);
const tableData = ref([]);
const columnsTable = ref(columns);

const columnsTableCom = computed(() => {
  return columnsTable.value.map((item) => {
    if (item.dataIndex === 'scene') {
      item.filters = item.filters
        .filter((v) => isCheckAuth || v.value !== '3')
        .filter((v) => checkResourceAuth('inferService') || v.value !== '2')
        .filter((v) => checkResourceAuth(['trainDev', 'trainTask']) || v.value !== '1')
        .filter((v) => checkResourceAuth(['datasetAnalyse', 'datasetClean', 'datasetEnhance']) || v.value !== '4');
    }
    return item;
  });
});

const fetchGpuType = async (params) => {
  try {
    const res = await getGpuTypeApi(params);

    if (res.code === 0) {
      let { data } = res;
      columnsTable.value = columnsTable.value.map((item) => {
        let obj = {
          ...item,
        };
        // if (item.dataIndex === 'gpuCardType') {
        //   obj = {
        //     ...item,
        //     filters: data
        //       .map((item) => {
        //         return { value: item.gpuCardTypeName, text: item.gpuCardTypeName };
        //       })
        //       .concat([{ value: '', text: '--' }]),
        //   };
        // } else if (item.dataIndex === 'gpuCardType2') {
        //   obj = {
        //     ...item,
        //     filters: data
        //       .map((item) => {
        //         return { value: item.gpuCardTypeName, text: item.gpuCardTypeName };
        //       })
        //       .concat([{ value: '', text: 'CPU' }]),
        //   };
        // }

        if (item.dataIndex === 'gpuCardType') {
          obj = {
            ...item,
            filters: data
              .map((item) => {
                return { value: item.gpuCardTypeName, text: item.gpuCardTypeName };
              })
              .concat([{ value: '', text: 'CPU' }]),
          };
        }
        return obj;
      });
    }
  } catch (error) {
    console.log('error');
  }
};

const fetchCluster = async (params) => {
  try {
    const res = await getClusterApi(params);

    if (res.code === 0) {
      let { data } = res;
      columnsTable.value = columnsTable.value.map((item) => {
        let obj = {
          ...item,
        };
        if (item.dataIndex === 'clusterName') {
          obj = {
            ...item,
            filters: data.map((item) => {
              return { value: item.clusterName, text: item.clusterName };
            }),
          };
        }
        return obj;
      });
    }
  } catch (error) {
    console.error('error');
  }
};

const tableChange = (pagination, filters, sorter) => {
  tableParams.value.sortFiled = sorter.field;
  tableParams.value.isAsc = sorter.order === 'ascend';

  changePageNum(1);
};

const getTableData = async () => {
  loading.value = true;
  try {
    const res = await getResViewTableApi(paginationOptions.value);
    if (res.code === 0) {
      const { data, total } = res.data;
      formatScene(data); // 格式化scene数据
      tableData.value = data;
      Object.assign(paginationOptions.value, { total });
    } else {
      tableData.value = [];
      Object.assign(paginationOptions.value, { total: 0 });
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    tableData.value = [];
    Object.assign(paginationOptions.value, { total: 0 });
  }
};

const formatScene = (list) => {
  if (list && list.length > 0) {
    list.forEach((item) => {
      if (item.scene == SCENARIO_TYPE_MAP.ALL) {
        item.scene = [SCENARIO_TYPE_MAP.DATA, SCENARIO_TYPE_MAP.TRAIN, SCENARIO_TYPE_MAP.OPTIMIZE, SCENARIO_TYPE_MAP.INFERENCE];
      } else {
        item.scene = String(item.scene).split(',');
      }
    });
  }
};

//跳转资源组详情
const toDetail = (record) => {
  router.push(`/resource-group/detail?groupId=${record.id}`);
};

const changePageNum = (pageNum) => {
  paginationOptions.value.pageNum = pageNum;
  getTableData();
};
const changePageSize = (size) => {
  Object.assign(paginationOptions.value, { pageNum: 1, pageSize: Number(size) });
  getTableData();
};

const sortField = ref(undefined);
const isAsc = ref(undefined);

// 排序、分页、筛选
const handleChange = (pagination, filters, sorter) => {
  const { scene, gpuCardType, groupName, resourceType, clusterName, ...rest } = filters;
  if (sorter.order) {
    sortField.value = sorter.field;
    isAsc.value = sorter.order === 'ascend' ? 'asc' : 'desc';
  } else {
    sortField.value = undefined;
    isAsc.value = undefined;
  }
  const filterFormat = {
    attributes: groupName,
    scenes: scene,
    gpuCardTypes: gpuCardType,
    // occupyRateOrder: sorter.order === 'ascend' ? 'asc' : 'desc',
    types: resourceType,
    clusters: clusterName,
    sortField: sortField.value,
    sortOrder: isAsc.value,
    // pageNum: paginationOptions.value.pageNum,
    // pageSize: paginationOptions.value.pageSize,
  };
  paginationOptions.value.pageNum = 1;
  Object.assign(paginationOptions.value, filterFormat);
  getTableData();
};

onMounted(() => {
  fetchGpuType();
  fetchCluster();
  getTableData();
});
</script>

<style lang="less" scoped>
.resource-name {
  cursor: pointer;
  &:hover {
    color: #2bb4d6;
  }
}
.overview-title {
  font-weight: 600;
  color: #121f2c;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 14px;
}
.overview-title::before {
  content: '';
  width: 4px;
  height: 14px;
  background-color: @jt-primary-color;
  margin-right: 8px;
}
</style>
