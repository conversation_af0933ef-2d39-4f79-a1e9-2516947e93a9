import { ref } from 'vue';
import { OPTIMIZE_TASK_TYPE, OPTIMIZE_TASK_TYPE_EMPTY_MSG, tableStatusFilter } from '@/constants/modelOptimize.js';

export const instanceList = {
  development: { label: OPTIMIZE_TASK_TYPE.INC_PRETRAIN, name: OPTIMIZE_TASK_TYPE_EMPTY_MSG[OPTIMIZE_TASK_TYPE.INC_PRETRAIN] },
  trainingTask: { label: OPTIMIZE_TASK_TYPE.SFT, name: OPTIMIZE_TASK_TYPE_EMPTY_MSG[OPTIMIZE_TASK_TYPE.SFT] },
  dpoTaskConfig: { label: OPTIMIZE_TASK_TYPE.DPO, name: OPTIMIZE_TASK_TYPE_EMPTY_MSG[OPTIMIZE_TASK_TYPE.DPO] },
};

const development = {
  columns: [
    {
      title: '增量预训练任务名称',
      width: '12%',
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      ellipsis: true,
    },
    {
      title: '基础模型',
      width: '15%',
      dataIndex: 'baseModelName',
      key: 'baseModelName',
      ellipsis: true,
      filtersInit: [],
    },
    {
      title: '创建人',
      width: '5%',
      dataIndex: 'createUserName',
      key: 'createUserName',
    },
    {
      title: '任务状态',
      width: '8%',
      dataIndex: 'status',
      key: 'status',
      filtersInit: tableStatusFilter(OPTIMIZE_TASK_TYPE.INC_PRETRAIN),
    },
    {
      title: '所属资源组',
      width: '15%',
      dataIndex: 'resourceGroupName',
      key: 'resourceGroupName',
      filtersInit: [],
    },
    {
      title: '实例数',
      width: '5%',
      dataIndex: 'replicas',
      key: 'replicas',
    },
    {
      title: '最近提交时间',
      with: '7%',
      dataIndex: 'submitTime',
      key: 'submitTime',
      sorterInit: true,
      sortOrder: '',
    },
    {
      title: '服务时长',
      with: '4%',
      dataIndex: 'useTime',
      key: 'useTime',
      // sorterInit: true,
      // sortOrder: '',
    },
    {
      title: '操作',
      width: '15%',
      dataIndex: 'options',
      key: 'options',
      fixed: 'right',
    },
  ],
};

const trainingTask = {
  columns: [
    {
      title: '有监督微调任务名称',
      width: '12%',
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      ellipsis: true,
    },
    {
      title: '基础模型',
      width: '15%',
      dataIndex: 'baseModelName',
      key: 'baseModelName',
      ellipsis: true,
      filtersInit: [],
      // filtersInit: [
      //   {
      //     text: 'CM-13.9B基础语言大模型-Base',
      //     value: 1,
      //   },
      // ],
    },
    {
      title: '创建人',
      width: '5%',
      dataIndex: 'createUserName',
      key: 'createUserName',
    },
    {
      title: '任务状态',
      width: '8%',
      dataIndex: 'status',
      key: 'status',
      filtersInit: tableStatusFilter(OPTIMIZE_TASK_TYPE.INC_PRETRAIN),
    },
    {
      title: '所属资源组',
      width: '15%',
      dataIndex: 'resourceGroupName',
      key: 'resourceGroupName',
      filtersInit: [],
    },
    {
      title: '实例数',
      width: '5%',
      dataIndex: 'replicas',
      key: 'replicas',
    },
    {
      title: '最近提交时间',
      with: '7%',
      dataIndex: 'submitTime',
      key: 'submitTime',
      sorterInit: true,
      sortOrder: '',
    },
    {
      title: '服务时长',
      with: '4%',
      dataIndex: 'useTime',
      key: 'useTime',
      // sorterInit: true,
      // sortOrder: '',
    },
    {
      title: '操作',
      width: '15%',
      dataIndex: 'options',
      key: 'options',
      fixed: 'right',
    },
  ],
};

const dpoTableConfig = {
  columns: [
    {
      title: '偏好对齐任务名称',
      width: '12%',
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      ellipsis: true,
    },
    {
      title: '基础模型',
      width: '15%',
      dataIndex: 'baseModelName',
      key: 'baseModelName',
      ellipsis: true,
      filtersInit: [],
      // filtersInit: [
      //   {
      //     text: 'CM-13.9B基础语言大模型-Base',
      //     value: 1,
      //   },
      // ],
    },
    {
      title: '创建人',
      width: '6%',
      dataIndex: 'createUserName',
      key: 'createUserName',
    },
    {
      title: '任务状态',
      width: '8%',
      dataIndex: 'status',
      key: 'status',
      filtersInit: tableStatusFilter(OPTIMIZE_TASK_TYPE.INC_PRETRAIN),
    },
    {
      title: '所属资源组',
      width: '15%',
      dataIndex: 'resourceGroupName',
      key: 'resourceGroupName',
      filtersInit: [],
    },
    {
      title: '实例数',
      width: '6%',
      dataIndex: 'replicas',
      key: 'replicas',
    },
    {
      title: '最近提交时间',
      with: '7%',
      dataIndex: 'submitTime',
      key: 'submitTime',
      sorterInit: true,
      sortOrder: '',
    },
    {
      title: '服务时长',
      with: '4%',
      dataIndex: 'useTime',
      key: 'useTime',
      // sorterInit: true,
      // sortOrder: '',
    },
    {
      title: '操作',
      width: '15%',
      dataIndex: 'options',
      key: 'options',
      fixed: 'right',
    },
  ],
};

export default {
  'inc-pretrain': ref(development),
  sft: ref(trainingTask),
  dpo: ref(dpoTableConfig),
};
