<template>
  <div class="banner-wrap" :class="{ collapse: collapse }">
    <jt-icon :type="collapse ? 'iconzhankai' : 'iconshouqi'" class="iconfont button-collapse" @click="handleToggle" />
    <h2>{{ welcomeTitle }}</h2>
    <p v-if="!collapse">汇聚多样化智能算力、深度学习框架，提供千亿级参数大模型设计、训练、微调、压缩、部署、托管、推理等全生命周期全流程服务能力及一体化研发工具链，大幅度提升大模型训推效率，助力企业数智化转型。</p>
    <a-button v-if="!collapse && !isEntranceBySasac()" type="primary" ghost class="banner-button" :href="`${urlPrefix}common-helpcenter#/homepage?platformCode=DMX_KFPT`"> 使用文档 </a-button>
    <h3 v-if="!collapse" class="banner-title" :style="isEntranceBySasac() ? { marginTop: '32px' } : {}">快速入门</h3>
    <div v-if="!collapse" class="banner-item-group">
      <div class="group-item">
        <h3>新建项目空间</h3>
        <p>新建项目空间进行协同AI开发，支持关联资源组和管理成员权限</p>
        <a-space :size="28">
          <a-button ghost type="link" class="button" @click="gotoCreateProjectSpace"> 立即新建 </a-button>
        </a-space>
      </div>
      <div class="group-item">
        <h3>开展AI开发</h3>
        <p>您可进入指定的项目空间，与项目成员协同开展数据准备、模型训练、模型推理</p>
        <a-space :size="28">
          <a-button v-if="datasetCleanSupported && !isEntranceBySasac() && checkResourceAuth('datasetClean')" ghost type="link" class="button" @click="showWelcomePage('dataCleaning')"> 数据准备 </a-button>
          <a-button v-if="checkResourceAuth('trainDev')" ghost type="link" class="button" @click="showWelcomePage('trainDev')"> 模型训练 </a-button>
          <a-button v-if="checkResourceAuth('inferService')" ghost type="link" class="button" @click="showWelcomePage('serviceManage')"> 模型推理 </a-button>
        </a-space>
      </div>
      <div class="group-item">
        <h3>管理项目资产</h3>
        <p>您可进入指定的项目空间，对项目空间的资产进行管理</p>
        <a-space :size="28">
          <a-button ghost type="link" class="button" @click="showWelcomePage('modelManage')"> 模型管理 </a-button>
          <a-button ghost type="link" class="button" @click="showWelcomePage('mirrorManage')"> 镜像管理 </a-button>
          <a-button ghost type="link" class="button" @click="showWelcomePage('fileManage')"> 文件管理 </a-button>
          <a-button v-if="checkResourceAuth('datasetManage')" ghost type="link" class="button" @click="showWelcomePage('dataSet')"> 数据集管理 </a-button>
        </a-space>
      </div>
    </div>
    <welcome-page v-model:open="welcomePageDisplay" :type="welcomePageType" @toDetail="gotoDetail" />
  </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';

import welcomePage from '@/components/welcomeModal.vue';

import useCheckCreatable from '@/views/project-space/create/checkCreatable';
import { getRedirectUrlPrefix } from '@/utils/index';
import { useStore } from 'vuex';
import { isEntranceBySasac } from '@/utils';
import { checkResourceAuth } from '@/utils/auth';

const store = useStore();
const urlPrefix = getRedirectUrlPrefix();

const router = useRouter();
const route = useRoute();

const welcomeTitle = ref('Hi，欢迎来到焕新社区！');
const welcomePageType = ref('');
const welcomePageDisplay = ref(false);
const showWelcomePage = (type) => {
  welcomePageDisplay.value = true;
  welcomePageType.value = type;
};
const gotoDetail = (url) => {
  router.push(url);
};

const datasetCleanSupported = computed(() => {
  return store.state?.poolPolicy?.datasetClean === '1';
});
const { checkCreatable, checking, remoteCheck } = useCheckCreatable();

const projectAuthority = ref(false);
const projectAuthorityCode = ref(null);

onMounted(async () => {
  const { avaialable, code } = await remoteCheck();
  projectAuthority.value = avaialable;
  projectAuthorityCode.value = code;
});

onBeforeMount(async () => {
  // 初始化国资title
  if (isEntranceBySasac()) {
    welcomeTitle.value = 'Hi，欢迎来到大模型开发平台！';
  }
});

const gotoCreateProjectSpace = async () => {
  // if (!(await checkCreatable())) {
  //   return;
  // }
  let path = '/project-space/create?previousPagePath=' + route.path;
  router.push(path);
};

// const gotoCreateProjectSpace = async () => {
//   if (projectAuthority.value === true && projectAuthorityCode.value === 0) {
//     console.log('if判断');
//     //正常进入新建项目空间权限页面
//     let path = '/project-space/create?previousPagePath=' + route.path;
//     router.push(path);
//   } else {
//     console.log('else判断');
//     //跳转无权限页面
//     router.push('/no-project-auth');
//   }
// };

const collapse = ref(false);
const handleToggle = () => {
  collapse.value = !collapse.value;
  sessionStorage.setItem('overviewListBanner', collapse.value ? 'collapse' : '');
};
</script>

<style lang="less" scoped>
.banner-wrap {
  position: relative;
  height: 356px;
  padding: 20px;
  background-image: url('~@/assets/images/overview/banner-bg-img.png'), url('~@/assets/images/overview/banner-bg-color.png');
  background-size: contain;
  background-position: right top, left top;
  background-repeat: no-repeat, repeat-x;
  border-radius: 4px;
  border: 1px solid #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 20, 26, 0.04);
  > h2 {
    line-height: 28px;
    font-size: 20px;
    font-weight: @jt-font-weight-medium;
  }
  > p {
    color: @jt-text-color-primary-opacity07;
    width: 676px;
    line-height: 21px;
  }
  > h3 {
    margin-bottom: 8px;
  }
  &.collapse {
    height: auto;
    padding: 24px 20px;
    background: #fff;
  }
}
.banner-button {
  margin-top: 16px;
  margin-bottom: 16px;
}
.banner-item-group {
  display: flex;
}
.banner-item-group {
  display: flex;
}
.group-item {
  width: 100%;
  height: 140px;
  padding: 20px 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: right top;
  // background: linear-gradient(149deg, #ffffff 0%, rgba(255, 255, 255, 0.66) 100%);
  border: 1px solid #ffffff;
  backdrop-filter: blur(9px);
  box-shadow: 0px 4px 10px 0px rgba(0, 160, 204, 0.12);
  border-radius: @jt-border-radius;
  &:nth-of-type(1) {
    background-image: url('~@/assets/images/overview/banner-01.png'), linear-gradient(149deg, #ffffff 0%, rgba(255, 255, 255, 0.66) 100%);
  }
  &:nth-of-type(2) {
    margin: 0 16px;
    background-image: url('~@/assets/images/overview/banner-02.png'), linear-gradient(149deg, #ffffff 0%, rgba(255, 255, 255, 0.66) 100%);
  }
  &:nth-of-type(3) {
    background-image: url('~@/assets/images/overview/banner-03.png'), linear-gradient(149deg, #ffffff 0%, rgba(255, 255, 255, 0.66) 100%);
  }
  h3 {
    font-size: @jt-font-size-base;
    color: #002833;
    line-height: 20px;
    margin-bottom: 8px;
    font-weight: @jt-font-weight-medium;
  }
  p {
    width: 240px;
    font-size: @jt-font-size-sm;
    color: #4c5a5e;
    line-height: 20px;
    margin-bottom: 12px;
  }
  .button {
    padding: 0;
    height: auto;
    border: none;
  }
  .button:hover {
    color: #2bb4d6;
  }
}
.button-collapse {
  position: absolute;
  right: 20px;
  top: 24px;
  background-color: #fff;
  display: inline-block;
  height: 24px;
  line-height: 26px;
  width: 24px;
  border-radius: 2px;
  border: 1px solid rgba(0, 20, 26, 0.15);
  text-align: center;
  cursor: pointer;
  color: rgba(0, 20, 26, 0.45);
  transition: 0.3s all;
  &:hover {
    color: @jt-primary-color;
    border-color: @jt-primary-color;
  }
}
</style>
