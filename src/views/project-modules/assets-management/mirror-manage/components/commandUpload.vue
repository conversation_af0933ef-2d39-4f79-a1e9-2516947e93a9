<template>
  <div class="command">
    <jt-loading v-if="loadingShow"></jt-loading>
    <div class="dev-env">
      <div class="dev-left"></div>
      <div>环境准备</div>
    </div>
    <div class="warpper-top">
      <a-steps progress-dot :current="6" direction="vertical">
        <a-step title="Step 1" class="step lineFeed" :description="step1" />
        <a-step title="Step 2" class="step lineFeed" :description="step2" />
        <a-step title="Step 3" class="step lineFeed" :description="step3" />
        <a-step title="Step 4" class="step" description=" 重新加载配置：systemctl daemon-reload" />
        <a-step title="Step 5" class="step" description="重启docker: systemctl restart docker" />
        <a-step title="Step 6" class="step" :description="step6" />
      </a-steps>
      <div class="dev-env" style="margin-top: 50px">
        <div class="dev-left"></div>
        <div>推送步骤</div>
      </div>
      <a-steps progress-dot :current="4" direction="vertical">
        <a-step title="Step 1" class="step lineFeed" :description="pushStep1" />
        <a-step title="Step 2" class="step lineFeed" :description="pushStep2" />
        <a-step title="Step 3" class="step lineFeed" description="刷新镜像管理下的自定义镜像文件列表页面，查看新推送的镜像是否出现在镜像列表中" />
      </a-steps>
    </div>
  </div>
</template>

<script>
import { mirrorManageApi } from '@/apis';
export default {
  name: 'CommmandUpload',
  data() {
    return {
      loadingShow: false,
      username: '',
      userpwd: '',
      harborUrl: '',
      baseUrl: '',
      loginUser: '',
      step1: '确保你推送镜像的机器连接了焕新社区能登陆OA的网络（如果windows电脑连了焕新社区办公网，可在windows电脑上安装VMware虚拟机，\n在VMware虚拟机中使用NAT模式启动Linux实例，即可在VMware的Linux实例上推送镜像）',
      step2: '推送镜像的机器上添加域名解析: echo "aiipregistry.jiutian.hq.cmcc" >> /etc/hosts',
      step3: '推送镜像的机器上安装docker并修改 /etc/docker/daemon.json 文件（如果没有就创建新的），增加如下内容:\n{\n"insecure-registries": ["aiipregistry.jiutian.hq.cmcc:80"]\n}',
      step6: '登陆harbor: docker login aiipregistry.jiutian.hq.cmcc:80 -u zhiqin-dev -p LLCQyhi9',
      pushStep1: '对本地镜像打tag: docker tag [镜像ID] aiipregistry.jiutian.hq.cmcc:80/zhiqin-dev/[镜像名称]:[镜像版本]\n例如：docker tag eb516548c180 aiipregistry.jiutian.hq.cmcc:80/zhiqin-dev/nginx:0.0.1',
      pushStep2: '推送镜像：docker push aiipregistry.jiutian.hq.cmcc:80/zhiqin-dev/[镜像名称]:[镜像版本]\n例如：docker push aiipregistry.jiutian.hq.cmcc:80/zhiqin-dev/nginx:0.0.1',
    };
  },
  created() {
    this.getUserInfo();
  },
  methods: {
    getUserInfo() {
      const sendData = {
        poolId: this.$store.state.poolInfo.id,
        projectId: this.$store.state.projectId,
      };
      this.loadingShow = true;
      mirrorManageApi.getCommandData(sendData).then((res) => {
        this.loadingShow = false;
        if (res.code == 0) {
          this.username = res.data.username;
          this.userpwd = res.data.password;
          this.harborUrl = res.data.registryUrl;
          this.baseUrl = this.harborUrl.substring(0, this.harborUrl.indexOf(':'));
          this.loginUser = `-u ${this.username} -p ${this.userpwd} `;
          this.step2 = `推送镜像的机器上添加域名解析: echo "${this.baseUrl}" >> /etc/hosts`;
          this.step3 = `推送镜像的机器上安装docker并修改 /etc/docker/daemon.json 文件（如果没有就创建新的），增加如下内容:\n
                        {\n
                        "insecure-registries": ["${this.harborUrl}"]\n
                        }`;
          this.step6 = `登陆harbor: docker login ${this.harborUrl} ${this.loginUser}`;
          this.pushStep1 = `对本地镜像打tag: docker tag [镜像ID] ${this.harborUrl}/${this.username}/[镜像名称]:[镜像版本]\n
                            例如：docker tag eb516548c180 ${this.harborUrl}/${this.username}/nginx:0.0.1`;
          this.pushStep2 = `推送镜像：docker push ${this.harborUrl}/${this.username}/[镜像名称]:[镜像版本]\n
                            例如：docker push ${this.harborUrl}/${this.username}/nginx:0.0.1`;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.content-wrap {
  padding-top: 26px;
  position: relative;
}

:deep(.ant-steps-dot .ant-steps-item-content) {
  width: 75%;
}

.warpper-top {
  font-size: 14px;
}

.step {
  margin-top: 32px;
  line-height: 20px;
}

.lineFeed {
  white-space: pre-line;
}

.dev-env {
  height: 30px;
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 14px;

  .dev-left {
    width: 4px;
    height: 14px;
    background-color: @jt-primary-color;
    margin-right: 8px;
  }
}

:deep(.ant-steps-item-description) {
  line-height: 16px;
  margin-top: 10px;
}
</style>
