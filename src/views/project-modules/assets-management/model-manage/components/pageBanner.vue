<template>
  <jt-list-banner :title="`欢迎使用模型管理！`" storage-key="DEVELOPMENT-ENVIRONMENT-KEY" :banner-style="{ margin: '0 0 20px' }" :banner-img="bannerImg">
    <div class="group-item">
      <h3>预置模型</h3>
      <p class="i-text">平台预置基础语言大模型等，支持快速发起预训练、微调、压缩、评估任务，也可进行一键部署，高效构建推理服务，对外提供高性能的AI推理服务</p>
      <div class="button-group">
        <span class="button banner-link" @click="emits('viewNow')">立即查看 <jt-icon type="iconjiantouyou" /> </span>
      </div>
    </div>
    <div class="group-item">
      <h3>自定义模型</h3>
      <p class="i-text">您可在当前项目空间内新建模型，支持多框架模型的统一纳管。通过多种方式导入上传训练好的模型，支持模型的多版本管理</p>
      <div class="button-group" @click="emits('createNew')">
        <span class="button banner-link" @click="emits('createNew')">立即新建 <jt-icon type="iconjiantouyou" /> </span>
      </div>
    </div>
  </jt-list-banner>
</template>

<script setup>
import { defineEmits } from 'vue';
import { useWindow } from '@/hooks/useWindow';
const bannerImg = require('@/assets/images/list-banner/pic-mxgl.png');
const emits = defineEmits(['createNew', 'viewNow']);
const { onResize } = useWindow();
onResize(() => {
  let groupItem = document.querySelector('.group-item');
  const itemHeight = groupItem.offsetHeight;

  let groupItemText = document.querySelectorAll('.i-text');
  const arrHeight = [];
  groupItemText.forEach((item) => {
    arrHeight.push(item.offsetHeight);
  });
  const max = Math.max(...arrHeight);

  const diff = itemHeight - max;

  if (diff < 30) {
    const fixHeight = itemHeight + 10;
    document.querySelectorAll('.group-item').forEach((groupItem) => {
      groupItem.style.minHeight = fixHeight + 'px';
    });
  } else if (diff > 48) {
    document.querySelectorAll('.group-item').forEach((groupItem) => {
      groupItem.style.minHeight = itemHeight - 20 + 'px';
    });
  }
});
</script>
<style scoped>
:deep(.banner-img) {
  right: 57px;
}
</style>
