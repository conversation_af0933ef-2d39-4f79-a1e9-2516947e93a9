<template>
  <a-modal v-model:open="visible" @ok="handleOk" @cancel="handleCancel">
    <template #title>
      <div>本地上传</div>
    </template>

    <a-flex class="row-item">
      <p class="label">目标路径</p>
      <a-tooltip :title="sourcePath">
        <p class="path overflow-ellipsis">{{ sourcePath }}</p>
      </a-tooltip>
    </a-flex>

    <div :class="['cp-upload-dragger', setMyUploadClass]">
      <a-upload-dragger ref="myUploadRef" v-model:fileList="fileList" :max-count="1" name="file" :directory="false" :before-upload="handleBeforeUpload" :multiple="false" :action="uploadFileUrl" :headers="defaultHeaders" :data="extraData" :disabled="isDisabledUpload" @remove="handleDelete" @change="handleChange">
        <template v-if="uploadStatus !== 'done'">
          <p class="ant-upload-drag-icon">
            <jt-icon type="iconshujuji" class="file-icon"></jt-icon>
          </p>
          <template v-if="uploadStatus === '' || uploadStatus === 'removed'">
            <p class="ant-upload-text">点击或将文件拖拽到这里上传</p>
            <p class="ant-upload-hint">单个文件大小或压缩包大小不超过100M</p>
          </template>
        </template>
        <div v-else class="is-done-file">
          <p class="name">{{ currFile.name }}</p>
          <p class="size">文件大小：{{ currFile.size }}</p>
          <p class="date">上传日期：{{ currFile.date }}</p>
          <div class="file-btns">
            <a-button type="link" style="padding: 0; margin-right: 24px" @click="handleReSelect">重新选择</a-button>
            <a-button type="link" danger style="padding: 0" @click="handleDelete">删除</a-button>
          </div>
        </div>
      </a-upload-dragger>
    </div>

    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button class="kl-create-btn button" type="primary" :disabled="isDisabledConfirm" @click="handleOk">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue';
import { useStore } from 'vuex';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { message } from 'ant-design-vue';
import { getFileServiceUrl } from '../utils';
import { requestWithProjectId } from '@/request';
import { dataToSizeConversion } from '@/utils/storage';

const store = useStore();
const visible = defineModel('visible');
const uploadFileUrl = ref('');
// 是否选中安全鉴权，默认打开
// 默认的headers
const defaultHeaders = computed(() => {
  return { Authorization: `Bearer ${store.state.token}` };
});

watch(visible, async (value) => {
  if (value) {
    fileList.value = [];
    uploadStatus.value = '';
    uploadFileUrl.value = await getFileServiceUrl('UPLOAD_FILE_URL');
    // 临时地址
    // let temp = await getFileServiceUrl('UPLOAD_FILE_URL');
    // uploadFileUrl.value = temp.replace('https://*************/', '');
  }
});
const isDisabledConfirm = computed(() => {
  if (fileList.value.length > 0 && uploadStatus.value === 'done') return false;
  else return true;
});
// eslint-disable-next-line no-unused-vars
const props = defineProps({
  pathArr: {
    type: Array,
    default() {
      return [];
    },
  },
  currentBucket: {
    type: String,
    required: true,
  },
});
const emits = defineEmits(['uploadFileSuccess']);
// 目标路径
const sourcePath = computed(() => {
  if (props.pathArr.length) {
    return props.currentBucket + '/' + props.pathArr.join('/');
  } else {
    return props.currentBucket;
  }
});
const fileList = ref([]);
const currFile = ref({ name: '', size: '', date: '' });
const uploadStatus = ref('');
const isDisabledUpload = computed(() => {
  if (['done', 'uploading'].includes(uploadStatus.value)) return true;
  else return false;
});

const extraData = ref({});
const handleBeforeUpload = (file) => {
  const isLimit = file.size / 1024 / 1024 <= 100;
  if (!isLimit) {
    message.error('大小不应该超过100M');
  }

  // 文件夹
  // const isFileType = file.type || file.name.match(/\.[^.]+$/);
  // if (!isFileType) {
  //   message.error('文件类型不符合');
  // }
  const isFileType = true;

  if (isLimit && isFileType) {
    fileList.value = [...(fileList.value || []), file];
    extraData.value = {
      name: props.pathArr.length ? props.pathArr.join('/') + '/' + file.name : file.name,
      bucketName: props.currentBucket,
    };
  }

  return isLimit && isFileType;
};

const handleChange = (info) => {
  console.log('upload file info >>>>', info);
  const { file } = info;
  const { name, size, response } = file;
  const status = file.status || '';
  uploadStatus.value = status;
  if (!status) {
    fileList.value = [];
  }
  // file.status: uploading,done,error,removed
  if (status === 'done') {
    if (!!response && response.code === 0) {
      message.success('上传成功');
      currFile.value = {
        name,
        size: dataToSizeConversion(size).sizeTxt + dataToSizeConversion(size).units,
        date: dayjs().format('YYYY-MM-DD'),
      };
      // } else if (response?.code !== 0) {
      //   // 302 网络延迟
      //   uploadStatus.value = 'error';
    } else {
      message.error('文件获取失败，请稍后再试');
    }
  } else if (status === 'error') {
    message.error('文件获取失败，请稍后再试');
  }
};
const setMyUploadClass = computed(() => {
  if (uploadStatus.value === 'done') {
    return 'is-done-upload';
  } else if (uploadStatus.value === 'error') {
    return 'is-error-upload';
  } else if (uploadStatus.value === 'uploading') {
    return 'is-uploading-upload';
  } else {
    return '';
  }
});

const myUploadRef = ref();
const handleReSelect = async () => {
  await deleteStorageFile();
  uploadStatus.value = '';
  fileList.value = [];
  myUploadRef.value.$el.querySelector('input').click();
};

const handleDelete = async () => {
  await deleteStorageFile();
  uploadStatus.value = '';
  fileList.value = [];
};

// 删除存储桶的文件
const deleteStorageFile = async () => {
  const objectNames = [props.pathArr.join('/') + '/' + currFile.value.name];
  await requestWithProjectId.POST('/web/storage/v1/deleteObject', objectNames);
};

const handleOk = async () => {
  try {
    if (uploadStatus.value === 'done') {
      emits('uploadFileSuccess');
    } else {
      // message.error('文件获取失败，请稍后再试');
      deleteStorageFile();
    }
    setOperateResult();
  } finally {
    fileList.value = [];
    visible.value = false;
  }
};

const setOperateResult = () => {
  const params = {
    action: 'uploadFile',
    result: '1',
    operateObject: [sourcePath.value + '/' + currFile.value.name],
    paramDetail: '', // 上传操作无需传值，同步需传入同步的文件存储的完整路径
  };
  requestWithProjectId.POST('/web/storage/v1/operate/result', params);
};

const handleCancel = async () => {
  // if (!!fileList.value.length) {
  //   await handleDelete();
  // }
  visible.value = false;
};
</script>

<style lang="less" scoped>
.row-item {
  align-items: center;
  padding-bottom: 20px;
  padding-top: 24px;

  &.align-base {
    align-items: baseline;
  }
}

.label {
  font-weight: 400;
  font-size: 14px;
  color: #00141a;
  line-height: 21px;
  text-align: center;
  margin-right: 16px;
  word-break: keep-all;
}

.path {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 20, 26, 0.7);
  line-height: 21px;
  text-align: left;
}

.cp-upload-dragger {
  height: 192px;
  background: rgba(0, 20, 26, 0.02);
  border-radius: 2px;
  border: 1px dashed rgba(0, 20, 26, 0.15);
  margin-bottom: 28px;

  :deep(.ant-upload) {
    border: none;
    background-color: transparent;
    padding: 0;

    .ant-upload-drag-icon {
      margin-top: 40px;
      height: 48px;
      line-height: 48px;
    }
    .upload-file-icon {
      font-size: 42px;
      color: #00a0cc;
    }
  }
  :deep(.ant-upload-list) {
    display: none;
  }

  .ant-upload-text {
    font-weight: 400;
    font-size: 16px;
    color: rgba(0, 20, 26, 0.7);
    line-height: 24px;
  }

  .ant-upload-hint {
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 20, 26, 0.45);
    line-height: 22px;
  }
  &:hover:not(.is-disabled) {
    border-color: #00a0cc;
    background: rgba(230, 247, 250, 0.25);
  }
}

.is-done-upload {
  .is-done-file {
    cursor: default;
    padding: 32px 32px 24px;
    text-align: left;
    line-height: 22px;
    font-size: 14px;
    .name {
      font-weight: 500;
      color: #00141a;
    }

    .size {
      font-weight: 400;
      color: rgba(0, 20, 26, 0.7);
      margin: 16px 0 8px;
    }

    .date {
      font-weight: 400;
      color: rgba(0, 20, 26, 0.7);
    }

    .file-btns {
      margin-top: 24px;
      height: 22px;
      :deep(.ant-btn) {
        height: 22px;
      }
    }
  }
}

.is-error-upload,
.is-uploading-upload {
  :deep(.ant-upload-wrapper) {
    position: relative;
    width: 100%;

    .ant-upload-drag-icon {
      margin-top: 50px;
    }

    .ant-upload-list {
      position: absolute;
      top: 114px;
      margin: 0 24px;
      left: 0;
      right: 0;
      display: block;
    }
  }
}
</style>
