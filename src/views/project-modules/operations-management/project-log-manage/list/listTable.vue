<template>
  <jt-container-item class="list-container" style="padding-bottom: 48px">
    <a-flex class="log-bar">
      <a-flex align="center">
        <h1 class="title">操作日志</h1>
        <ExclamationCircleFilled class="warn-icon" />
        <div class="tips">仅保留近3个月内日志记录</div>
      </a-flex>
      <a-flex gap="8">
        <jt-search-input v-model:value="searchKey" placeholder="请输入操作人/操作/操作对象" style="width: 190px" @change="handleSearch"> </jt-search-input>
        <div class="space-select">
          <a-select v-model:value="activeEventResult" show-arrow placeholder="操作结果" class="result-select" style="width: 100px" :options="eventResultOptions" :allow-clear="true" :get-popup-container="(el) => el.parentNode"> </a-select>
          <a-select v-model:value="activeEventModule" show-arrow placeholder="操作模块" class="module-select" mode="multiple" :max-tag-count="1" allow-clear style="width: 188px; margin-top: -1px" :options="eventModuleOptions" :get-popup-container="(el) => el.parentNode"> </a-select>
        </div>

        <a-select v-model:value="rangePreset" style="width: 100px" :options="rangePresets"> </a-select>
        <a-range-picker v-model:value="selectedDates" :show-time="showTime" :format="timeFormat" :disabled="!rangePickerAvailable" :disabled-date="disabledDates" :allow-clear="false" style="width: 320px"> </a-range-picker>
        <a-button ghost type="primary" @click="handleOutport">导出</a-button>
      </a-flex>
    </a-flex>
    <div class="list-table">
      <a-config-provider>
        <template #renderEmpty>
          <jt-empty :show-operation="!searchKey" :title="emptyTitle"></jt-empty>
        </template>
        <a-table :columns="columns" :data-source="dataSource" :loading="dataSourceLoading" :scroll="{ x: 1300 }" :pagination="false" @change="handleTableChange">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex === 'eventTime'">
              <span class="hover" @click="navToDetail(record)"> {{ text || '--' }}</span>
            </template>
            <template v-if="column.dataIndex === 'eventResultName'">
              <div>
                <jt-tag :color="record.tagColor">{{ text }}</jt-tag>
              </div>
            </template>
          </template>
        </a-table>
      </a-config-provider>
      <jt-pagination :page-num="paginationOptions.pageNum" :page-size="paginationOptions.pageSize" :total="paginationOptions.total" @changePageSize="changePageSize" @changePageNum="changePageNum" />
    </div>
  </jt-container-item>
</template>

<script setup>
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { message } from 'ant-design-vue';
import { requestWithProjectId } from '@/request/index.js';
import { FILTER_CONFIG, useModuleColumns, useColumns } from './listTable.js';
import { exportFileBlob } from '@/utils/file.js';
import { AUTH_CHECKS } from '@/constants/management-platform/poolManage.js';
import { checkResourceAuth } from '@/utils/auth';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import { debounce } from 'lodash';

const store = useStore();
const router = useRouter();
// search
const searchKey = ref('');
// result
const activeEventResult = ref();
const eventResultOptions = FILTER_CONFIG.eventResultOptions;
const ACTIVE_TAG_COLOR = {
  1: 'green',
  0: 'red',
};
// module
const activeEventModule = ref();
const eventModuleOptions = ref([]);
const getModuleOptions = async () => {
  const res = await requestWithProjectId.GET('/web/eventrack/v1/event-modules');
  if (res.code === 0) {
    eventModuleOptions.value = useModuleColumns(res.data).filter((v) => {
      return AUTH_CHECKS.every((check) => checkResourceAuth(check.auth) || v.value !== check.value);
    });
  }
};
// datetime
const selectedDates = ref([]);
const showTime = FILTER_CONFIG.showTime;
const timeFormat = FILTER_CONFIG.timeFormat;

const rangePreset = ref('lastDay');
const rangePresets = FILTER_CONFIG.rangePresetList;
watch(rangePreset, () => {
  selectedDates.value = rangePresets.find((item) => item.value === rangePreset.value).getTimeValue();
});
const rangePickerAvailable = computed(() => rangePreset.value === 'custom');
const disabledDates = computed(() => {
  const currentRangePreset = rangePresets.find((item) => item.value === rangePreset.value);
  return currentRangePreset?.disabledDate;
});
// table
const emptyTitle = '操作日志';
const columns = ref([]);
const dataSourceLoading = ref(false);
const dataSource = ref([]);
const paginationOptions = ref({
  total: 0,
  pageSize: 10,
  pageNum: 1,
});
const sort = ref(false);
const changePageNum = (pageNum) => {
  paginationOptions.value.pageNum = pageNum;
  getDataSource();
};
const changePageSize = (size) => {
  paginationOptions.value.pageNum = 1;
  paginationOptions.value.pageSize = size;
  getDataSource();
};
const getDataSource = async () => {
  dataSourceLoading.value = true;
  try {
    const params = {
      eventModule: activeEventModule.value || [],
      eventResult: activeEventResult.value || '',
      startTime: selectedDates.value ? dayjs(selectedDates.value[0]).format('YYYY-MM-DD HH:mm:00') : '',
      endTime: selectedDates.value ? dayjs(selectedDates.value[1]).format('YYYY-MM-DD HH:mm:00') : '',
      eventPlatform: 1,
      searchKey: searchKey.value,
      pageNum: paginationOptions.value.pageNum,
      pageSize: paginationOptions.value.pageSize,
      sortFiled: 'eventTime',
      isAsc: sort.value,
    };
    const res = await requestWithProjectId.POST('/web/eventrack/v1/event-logs', params);
    if (res.code === 0) {
      dataSource.value = res.data.data.map((item) => {
        item.eventTime = dayjs(item.eventTime).format('YYYY-MM-DD HH:mm:ss');
        item.eventResultName = eventResultOptions.filter((o) => o.value == item.eventResult)[0].label;
        item.tagColor = ACTIVE_TAG_COLOR[item.eventResult];
        return item;
      });
      paginationOptions.value.total = res.data.total;
    } else {
      message.error(res.msg || '请求错误，请稍后再试！');
    }
  } finally {
    dataSourceLoading.value = false;
  }
};

// 操作时间点击至详情
const navToDetail = (record) => {
  const searchParams = {
    searchInput: searchKey.value || '',
    eventResult: activeEventResult.value || '',
    eventModule: activeEventModule.value || [],
    startTime: selectedDates.value ? dayjs(selectedDates.value[0]).format('YYYY-MM-DD HH:mm:00') : '',
    endTime: selectedDates.value ? dayjs(selectedDates.value[1]).format('YYYY-MM-DD HH:mm:00') : '',
    dateRangePreset: rangePreset.value,
    pageNum: paginationOptions.value.pageNum,
    pageSize: paginationOptions.value.pageSize,
  };
  store.dispatch('updateSearchParams', searchParams);
  router.push({
    path: `/project-log-manage/detail/${record.id}`,
    query: {
      eventTime: record.eventTime,
    },
  });
};

const handleTableChange = (pagination, filter, sorter) => {
  if (Object.keys(sorter).length !== 0) {
    const order = sorter.order;
    sort.value = order === 'ascend' ? true : false;
  }
  paginationOptions.value.pageNum = 1;
  getDataSource();
};

// 导出
const handleOutport = async () => {
  const params = {
    eventModule: activeEventModule.value || [],
    eventResult: activeEventResult.value || '',
    startTime: selectedDates.value ? dayjs(selectedDates.value[0]).format('YYYY-MM-DD HH:mm:00') : '',
    endTime: selectedDates.value ? dayjs(selectedDates.value[1]).format('YYYY-MM-DD HH:mm:00') : '',
    eventPlatform: 1,
    searchKey: searchKey.value,
    pageNum: paginationOptions.value.pageNum,
    pageSize: paginationOptions.value.pageSize,
    sortFiled: 'eventTime',
    isAsc: sort.value,
  };
  exportFileBlob({ url: '/web/eventrack/v1/export-event-logs', method: 'POST', params });
};

const handleSearch = () => {
  paginationOptions.value.pageNum = 1;
  paginationOptions.value.total = 0;
  nextTick(() => {
    getDataSource();
  });
};

watch(
  // () => [searchKey.value, activeEventResult.value, activeEventModule.value, selectedDates.value],
  () => [activeEventResult.value, activeEventModule.value, selectedDates.value],
  () => {
    paginationOptions.value.pageNum = 1;
    paginationOptions.value.total = 0;
    nextTick(() => {
      getDataSource();
    });
  }
);

const storeSearchParams = computed(() => store.state?.projectLogManage?.searchParams || {});
const setSearchParamsByStore = async ({ searchInput, eventResult, eventModule, startTime, endTime, dateRangePreset, pageNum, pageSize, isAsc }) => {
  searchKey.value = searchInput || '';
  activeEventResult.value = eventResult || null;
  activeEventModule.value = eventModule || [];
  rangePreset.value = dateRangePreset || 'lastDay';
  selectedDates.value = [startTime, endTime];
  paginationOptions.value.pageNum = pageNum || 1;
  paginationOptions.value.pageSize = pageSize || 10;
};
onMounted(async () => {
  await getModuleOptions();
  if (Object.keys(storeSearchParams.value).length) {
    setSearchParamsByStore(storeSearchParams.value);
    setTimeout(() => {
      // 每次从详情页进来获取到store中的筛选数据后清空store的存储，避免从其他页面进来存在筛选条件
      store.dispatch('updateSearchParams', {});
    });
  }
  // 时间选择：默认为近一天
  selectedDates.value = rangePresets.find((item) => item.value === rangePreset.value).getTimeValue();
  useColumns(columns);
  getDataSource();
});
</script>

<style lang="less" scoped>
.list-container {
  .log-bar {
    justify-content: space-between;
    align-items: center;
    line-height: 22px;

    .title {
      font-weight: 600;
      font-size: 16px;
      color: #00141a;
    }
    .warn-icon {
      font-size: 16px;
      color: rgba(255, 140, 25, 1);
      margin: 0 9px 0 13px;
    }

    .tips {
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 20, 26, 0.7);
    }

    .space-selec {
      display: flex;
    }
  }

  .list-table {
    margin-top: 16px;
  }
}
.hover {
  cursor: pointer;
  &:hover {
    color: @jt-primary-color;
  }
}
:deep .ant-select .ant-select-selection-placeholder {
  color: rgba(0, 20, 26, 0.7);
}

//表格内容color
:deep(.ant-table-cell),
:deep(.pagination) {
  color: rgba(0, 20, 26, 0.7);
}

.module-select {
  margin-left: -1px;
  :deep(.ant-select-selector) {
    height: 100%;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
.result-select {
  :deep(.ant-select-selector) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}
</style>
