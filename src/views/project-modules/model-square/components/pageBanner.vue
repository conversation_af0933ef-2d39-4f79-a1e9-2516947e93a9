<template>
  <jt-list-banner :title="`欢迎使用模型广场！`" storage-key="DEVELOPMENT-ENVIRONMENT-KEY" :banner-style="{ margin: '0 0 20px' }" :banner-img="bannerImg">
    <div class="group-item">
      <h3>平台预置模型</h3>
      <p class="i-text">模型广场提供多种模型，包含基础语言大模型、多模态大模型、三方开源模型、行业热门模型等，支持模型探索、体验等功能</p>
    </div>
    <div class="group-item">
      <h3>模型体验</h3>
      <p class="i-text">支持在线体验模型对话能力，基于预置模型快速发起增量预训练、有监督微调、偏好对齐、压缩、评估任务，也可进行一键部署推理任务</p>
    </div>
  </jt-list-banner>
</template>

<script setup>
import { defineEmits } from 'vue';
import { useWindow } from '@/hooks/useWindow';
const bannerImg = require('@/assets/images/list-banner/model-square.png');
const { onResize } = useWindow();
onResize(() => {
  let groupItem = document.querySelector('.group-item');
  const itemHeight = groupItem.offsetHeight;

  let groupItemText = document.querySelectorAll('.i-text');
  const arrHeight = [];
  groupItemText.forEach((item) => {
    arrHeight.push(item.offsetHeight);
  });
  const max = Math.max(...arrHeight);

  const diff = itemHeight - max;

  if (diff < 30) {
    const fixHeight = itemHeight + 10;
    document.querySelectorAll('.group-item').forEach((groupItem) => {
      groupItem.style.minHeight = fixHeight + 'px';
    });
  } else if (diff > 48) {
    document.querySelectorAll('.group-item').forEach((groupItem) => {
      groupItem.style.minHeight = itemHeight - 20 + 'px';
    });
  }
});
</script>
<style scoped>
:deep(.banner-img) {
  right: 57px;
}
</style>
