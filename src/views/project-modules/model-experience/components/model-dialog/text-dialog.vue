<template>
  <div class="model-dialog">
    <div class="model-dialog-main">
      <div v-if="paramInfo.length" class="talk-content">
        <div v-for="(chatItem, chatIndex) in paramInfo" :key="chatIndex" class="one-chat">
          <h4>{{ chatItem.serviceName }}</h4>
          <div class="init-welcome-message">
            <img :src="modelIcon" alt="" class="icon-img" />
            <div class="introduce">
              <h2>您好，我是你的AI助手。</h2>
              <p>{{ getIntroduce(modelType) }}</p>
            </div>
          </div>
          <div v-for="(item, index) in chatItem.historyMessage" :key="index" class="dialog-case">
            <div class="case-user">
              <img :src="userIcon" alt="" />
              <p class="user-question" v-html="formatText(item.question)" />
            </div>
            <div class="case-answer">
              <img :src="modelIcon" alt="" />
              <div class="answer-box">
                <!-- 回答展示 -->
                <p v-if="item.status === 'thinking'" class="box-txt"><LoadingOutlined class="front-icon" />正在思考</p>
                <p v-if="item.status === 'normal'" class="box-txt">{{ item.answer + '...' }}</p>
                <div v-if="item.status == 'done'" class="box-txt">
                  {{ item.answer }}
                </div>
                <div v-if="item.status == 'exception'" class="box-txt">
                  <p v-if="getModelId(chatItem)">{{ item.answer }}</p>
                  <p v-else class="custom-exception-tip">模型服务暂不可用，请前往<span class="word-btn" @click="goServiceManage(chatItem)">推理服务</span>检查当前推理服务状态或接口格式（仅支持openai接口）</p>
                </div>
                <div v-if="!item.status" class="box-txt">{{ getAnswer(item) }}</div>
                <!-- 操作按钮 -->
                <span v-if="item.isStopped" class="stopped-tip">已停止生成</span>
                <div v-if="(item.status === 'done' || (!item.status && item.answer)) && item.messageId" class="box-bottom">
                  <div class="operate-btn">
                    <jt-icon type="iconzan" class="zan-icon" :style="getHandColor(item.isLiked).firstHand" @click="getLike(chatIndex, index, item.messageId)" />
                    <jt-icon type="iconcai" class="cai-icon" :style="getHandColor(item.isLiked).secondHand" @click="getDisLike(chatIndex, index, item.messageId)" />
                    <jt-icon type="iconfile-copy" class="copy-icon" @click="hanleCopyTxt(item.answer)" />
                    <span class="line-icon" />
                    <div v-if="!item.isLoading" class="player" @click="handleReadTxt(chatIndex, index, item.messageId)">
                      <span v-if="item.isPlay" class="sound-btn">
                        <img :src="voiceBreakIcon" alt="" />
                        <jt-icon type="iconhuakuaishuxian1" />
                      </span>

                      <jt-icon v-else type="iconbofang2" class="voice-icon" />
                    </div>
                    <LoadingOutlined v-if="item.isLoading" />
                  </div>
                  <span v-if="isRegetBtn(chatIndex, index, item)" class="again-btn" @click="reGet(chatIndex, index, item)"><jt-icon type="iconzhongxinshengcheng" class="again-icon" />重新生成</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="empty-box">
        <div>
          <img :src="emptyImg" alt="" />
          <p>暂未选择服务</p>
        </div>
      </div>
    </div>

    <div class="question-box">
      <div class="question-box-top">
        <a-button type="link" :disabled="!isSupportClear()" :class="isSupportClear() ? 'clear-btn' : 'clear-disabled-btn'" @click="goClear"><jt-icon type="icongeshihua" class="clear-icon" />清空对话</a-button>
        <span v-if="isHasThinking" class="stop-btn" @click="goStop">
          <jt-icon type="icontingzhi2" class="stop-icon" />
          <span>停止生成</span>
        </span>
        <span v-if="checkResourceAuth('promptManage')" class="tip-word" @click="showDrawer()"><jt-icon type="iconcopy" class="tip-word-icon" />提示词模板</span>
      </div>
      <div :class="isShowQuestionBoxBoder ? 'question-box-center box-focus' : 'question-box-center'">
        <a-textarea v-model:value="textVal" show-count :maxlength="20000" placeholder="请在这里输入您的问题，可通过ctrl+回车换行" :auto-size="{ minRows: 1, maxRows: 2 }" class="question-textarea" @focus="handleFocus" @blur="handleBlur" @pressEnter="enterSend" />
        <a-button v-if="isSupportSend()" type="link" class="send-btn" @click="onSend()"><img :src="sendBtn" alt="" /></a-button>
        <a-button v-else type="link" :disabled="true" class="send-btn"><img :src="sendDisableBtn" alt="" /></a-button>
      </div>
      <p class="question-box-tip">本平台内容为 AI 生成，不代表开发者立场，请勿删除或修改本标记。</p>
    </div>

    <!-- 语音播报 -->
    <audio ref="audioEl"></audio>
    <!-- 提示词模板 -->
    <tip-word-drawer :open-drawer="openDrawer" @closed="drawerClosed" />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { LoadingOutlined } from '@ant-design/icons-vue';
import emptyImg from '@/assets/images/model-experience/empty-status.png';
import userIcon from '@/assets/images/model-experience/user-icon.png';
import modelIcon from '@/assets/images/model-experience/model-icon.png';
import voiceBreakIcon from '@/assets/images/model-experience/time-out.png';
import sendBtn from '@/assets/images/model-experience/send-btn.png';
import sendDisableBtn from '@/assets/images/model-experience/send-disable-btn.png';
import { message } from 'ant-design-vue';
import { handleCopy } from '@/utils/index.js';
import tipWordDrawer from '../tip-word-drawer/tip-word-drawer.vue';
import { dataURItoBlob, formatText, getIntroduce, generateUUID, hasNormalOrThinkingStatus } from '../../utils.js';
import { reqLike, reqDislike, reqBase64, reqStop } from '@/apis/modelExperience.js';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { getProxyPrefix } from '@/config';
import { openInNewTab } from '@/utils';
import { checkResourceAuth } from '@/utils/auth';

const route = useRoute();
const router = useRouter();
const store = useStore();
const currentGroupId = ref('');
const modelType = computed(() => store.state.modelExperience.modelType);
const serviceList = computed(() => store.state.modelExperience.serviceList);
const paramInfo = computed(() => store.state.modelExperience.paramInfo);
const isChatProgress = computed(() => store.state.modelExperience.isChatProgress);
const textareaRef = ref(null);
const textVal = ref('');
const isShowQuestionBoxBoder = ref('');
const openDrawer = ref(false);
let As = true; //暂停流式接口

const enterSend = (event) => {
  //ctrl+enter键-光标处换行
  event.preventDefault();
  if (event.ctrlKey && event.key === 'Enter') {
    insertNewlineAtCursor();
  }
  if (!event.ctrlKey && isSupportSend()) {
    onSend();
  }
};

const onSend = () => {
  As = true;
  store.commit('updateIsAdd', true);
  scrollToBottom();
  const userQuestion = textVal.value.trim();
  const questionId = generateUUID();

  const newRecord = {
    status: 'thinking',
    question: userQuestion,
    questionId,
    answer: '',
  };
  const addGroupId = generateUUID();

  paramInfo.value.map((obj, index) => {
    obj.historyMessage.push(newRecord);
    const { modelId, serviceId, chatParam, adjustableParamsConfig } = obj;
    const { chatId } = chatParam || {};
    //沒有currentGroupId.value-新增对话,否则是继续对话或者重新对话
    let preBuiltObj;
    let customObj;
    if (currentGroupId.value) {
      preBuiltObj = {
        groupId: currentGroupId.value,
        chatId,
        serviceType: 0,
        modelId,
        input: userQuestion,
        questionId,
        ...chatParam,
        adjustableParamsConfig: JSON.stringify({ ...adjustableParamsConfig }),
      };
      customObj = {
        groupId: currentGroupId.value,
        chatId,
        serviceType: 1,
        serviceId,
        input: userQuestion,
        questionId,
      };
    } else {
      const newChatParam = { ...chatParam };
      delete newChatParam.chatId;
      preBuiltObj = {
        groupId: addGroupId,
        serviceType: 0,
        modelId,
        input: userQuestion,
        questionId,
        ...newChatParam,
        adjustableParamsConfig: JSON.stringify({ ...adjustableParamsConfig }),
      };
      customObj = {
        groupId: addGroupId,
        serviceType: 1,
        serviceId,
        input: userQuestion,
        questionId,
      };
    }

    const paramObj = modelId ? preBuiltObj : customObj;
    connectSSE(paramObj, index);
  });
  store.commit('updateParamInfo', paramInfo.value);
  textVal.value = '';
};

const insertNewlineAtCursor = () => {
  const textareaDom = textareaRef.value;
  //光标开始和结束位置
  const startPos = textareaDom.selectionStart;
  const endPos = textareaDom.selectionEnd;
  textVal.value = textVal.value.substring(0, startPos) + '\n' + textVal.value.substring(endPos);
  setTimeout(() => {
    //重设光标位置
    textareaDom.setSelectionRange(startPos + 1, startPos + 1);
    textareaDom.focus();
  }, 0);
};

const isRegetBtn = (chatIndex, index) => {
  const { historyMessage } = paramInfo.value[chatIndex];
  return index == historyMessage.length - 1;
};

const reGet = (chatIndex, index, item) => {
  As = true;
  const { modelId, serviceId, chatParam, adjustableParamsConfig } = paramInfo.value[chatIndex];
  const { chatId } = chatParam;
  const { question, questionId, messageId } = item;
  let paramObj;

  const preParam = {
    groupId: currentGroupId.value,
    chatId,
    messageId,
    serviceType: 0,
    modelId,
    input: question,
    questionId,
    ...chatParam,
    adjustableParamsConfig: JSON.stringify({ ...adjustableParamsConfig }),
  };
  const customObj = {
    groupId: currentGroupId.value,
    chatId,
    messageId,
    serviceType: 1,
    serviceId,
    input: question,
    questionId,
  };
  paramObj = modelId ? preParam : customObj;
  let currentDialogCase = {};
  currentDialogCase.status = 'thinking';
  currentDialogCase.question = question;
  currentDialogCase.questionId = questionId;
  currentDialogCase.answer = '';
  currentDialogCase.isStopped = false;
  currentDialogCase.isLiked = 0;
  paramInfo.value[chatIndex].historyMessage[index] = currentDialogCase;
  store.commit('updateParamInfo', paramInfo.value);

  connectSSE(paramObj, chatIndex);
};

const eventSource = ref([]);
const controller = new AbortController();
const signal = controller.signal;
const connectSSE = (paramObj, chatIndex) => {
  stopVoice();

  const newList = paramInfo.value;
  const { historyMessage } = newList[chatIndex] || {};
  const currentDialogCase = historyMessage[historyMessage.length - 1];
  const obj = { ...paramObj };
  // if (currentGroupId.value) {
  //   delete obj.input;
  // }
  const isDev = process.env.NODE_ENV === 'development';
  const isResearch = window.location.origin?.includes('167');
  const proxyPrefix = getProxyPrefix();
  const baseURL = isDev ? `/api/${proxyPrefix}` : isResearch ? '' : '/kunlun';
  const poolId = store.state.poolInfo.id;
  const projectId = store.state.projectId;

  try {
    eventSource.value[chatIndex] = fetchEventSource(`${baseURL}/web/experience/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${store.state.token}`,
        Accept: 'text/event-stream',
        Connection: 'keep-alive',
        Poolid: poolId,
        Projectid: projectId,
      },
      body: JSON.stringify(obj),
      signal: signal,
      onopen: () => {
        console.log('SSE-open');
      },
      onmessage: (response) => {
        scrollToBottom();
        onReceive(chatIndex, response, paramObj);
      },
      onerror: (error) => {
        console.log('SSE-onerror');
        currentDialogCase.status = 'done';
        throw error;
      },
      onclose: () => {
        console.log('SSE-onclose');
        const { historyMessage } = newList[chatIndex] || {};
        const currentDialogCase = historyMessage[historyMessage.length - 1];
        const { answer, status } = currentDialogCase || {};
        if (status == 'thinking') {
          currentDialogCase.status = 'done';
        }
        if (!answer) {
          currentDialogCase.answer = '抱歉，请稍后尝试';
        }
      },
    });
  } catch (error) {
    goStop();
  }
};

const scrollToBottom = () => {
  const divs = document.querySelectorAll('.one-chat');
  window.requestAnimationFrame(() => {
    divs.forEach((item) => {
      item && scroll(item);
    });
  });
};

const scroll = (elm) => {
  if (elm) {
    elm.scrollTop = elm.scrollHeight;
  }
};

const onReceive = (chatIndex, response, paramObj) => {
  const { historyMessage, chatParam } = paramInfo.value[chatIndex];
  if (!As) return;

  const { data, event, id: totalId } = response || {};
  if (data == '[DONE]') {
    historyMessage[historyMessage.length - 1].status = 'done';
    store.commit('updateParamInfo', paramInfo.value);
    return;
  }

  let currentDialogCase = {
    question: paramObj.input,
    questionId: paramObj.questionId,
  };
  if (totalId) {
    const newTotalId = JSON.parse(totalId);
    const { groupId, chatId } = newTotalId || {};
    const newChatParam = {
      ...chatParam,
      chatId,
    };
    paramInfo.value[chatIndex].chatParam = newChatParam;
    router.push(`/model-experience/detail/${groupId}`);
  }

  if (event == 'exception') {
    currentDialogCase.status = 'exception';
    currentDialogCase.answer = data;
  }

  if (event == 'done') {
    return;
  }

  if (event == 'normal' && data != '[DONE]') {
    //更新当前回答
    if (data) {
      const messageObj = JSON.parse(data);
      const { id } = messageObj || {};
      const str = messageObj.choices[0].delta.content;
      currentDialogCase.status = 'normal';
      currentDialogCase.answer = str;
      currentDialogCase.messageId = id;
    }

    if (totalId) {
      const newTotalId = JSON.parse(totalId);
      const { messageId } = newTotalId || {};
      currentDialogCase.messageId = messageId;
    }
  }
  historyMessage[historyMessage.length - 1] = currentDialogCase;
  store.commit('updateParamInfo', paramInfo.value);
  // console.log('4', paramInfo.value);
};

const goClear = () => {
  const updateDate = paramInfo.value.map((item, index) => {
    item.historyMessage = [];
    return item;
  });
  store.commit('updateParamInfo', updateDate);
  router.push('/model-experience');
};

const getStop = async (chatIndex, messageId) => {
  const { historyMessage } = paramInfo.value[chatIndex];
  try {
    const res = await reqStop(messageId);
    const { data } = res || {};
    if (data) {
      const newData = {
        ...historyMessage[historyMessage.length - 1],
        status: 'done',
        isStopped: true,
      };
      historyMessage[historyMessage.length - 1] = newData;
      store.commit('updateParamInfo', paramInfo.value);
      // console.log('6', newData);
    }
  } catch (error) {
    console.log(error);
  }
};

const goStop = () => {
  As = false;

  try {
    // eventSource.value = null;
    paramInfo.value.map(async (item, index) => {
      const { historyMessage } = item || {};
      const { messageId, status } = historyMessage[historyMessage.length - 1] || {};
      if (status == 'normal') {
        eventSource.value[index] = null;
        controller.abort();
        getStop(index, messageId);
      }
    });
  } catch (error) {
    console.log(error);
  }
};

const getAnswer = (item) => {
  const { answer } = item || {};
  let txt;
  //!status是详情页，回显即可
  txt = answer ? answer : '抱歉，请稍后尝试';
  return txt;
};

const getModelId = (data) => {
  //有modelId是预置，无modelId是推理服务
  const { modelId } = data || {};
  return modelId;
};

const goServiceManage = async (chatItem) => {
  const { serviceId } = chatItem || {};
  // router.push(`/service-manage/service-detail?instanceId=${serviceId}`);
  const route = router.resolve({ path: `/service-manage/service-detail`, query: { instanceId: serviceId } });
  openInNewTab(route.href);
};

//isLiked--- 0-未点,1-点赞,2-点踩
const getLike = async (chatIndex, index, messageId) => {
  try {
    const res = await reqLike(messageId);
    const { code, data } = res || {};
    const updateItem = paramInfo.value[chatIndex].historyMessage[index];
    updateItem.isLiked = data;
    if (code == '0') {
      data == 1 ? message.success('点赞成功') : message.success('取消点赞');
    } else {
      message.error('点赞失败,请稍后再试');
    }
  } catch (error) {
    message.error('点赞失败,请稍后再试');
    console.log(error);
  }
};

const getDisLike = async (chatIndex, index, messageId) => {
  try {
    const res = await reqDislike(messageId);
    const { code, data } = res || {};
    const updateItem = paramInfo.value[chatIndex].historyMessage[index];
    updateItem.isLiked = data;
    if (code == '0') {
      data == 2 ? message.success('点踩成功') : message.success('取消点踩');
    } else {
      message.error('点踩失败,请稍后再试');
    }
  } catch (error) {
    message.error('点踩失败,请稍后再试');
    console.log(error);
  }
};

const hanleCopyTxt = (value) => {
  handleCopy(value);
};

//语音播放
const audioEl = ref(null);
const currMessageId = ref(null);
const handleReadTxt = async (chatIndex, targetIndex, messageId) => {
  const song = paramInfo.value[chatIndex].historyMessage[targetIndex];
  song.isPlay = !song.isPlay;
  paramInfo.value.map((item) => {
    item.historyMessage.map((obj) => {
      if (obj.messageId != messageId) {
        obj.isLoading = false;
        obj.isPlay = false;
      }
    });
  });

  if (messageId == currMessageId.value) {
    //相同按钮
    if (song.isPlay) {
      audioEl.value.play();
      //语音结束
      audioEl.value.addEventListener('ended', function () {
        song.isPlay = false;
      });
    } else {
      audioEl.value && audioEl.value.pause();
    }
  } else {
    currMessageId.value = messageId;
    //不同按钮
    audioEl.value && audioEl.value.pause();
    song.isLoading = true;
    const audioData = await getBase64(messageId);
    try {
      const blobUrl = URL.createObjectURL(new Blob([audioData], { type: 'audio/mpeg' }));
      audioEl.value.src = blobUrl;
      audioEl.value.load();
      currMessageId.value && audioEl.value.play();
      //语音结束
      audioEl.value.addEventListener('ended', function () {
        song.isPlay = false;
      });
      song.isLoading = false;
    } catch (error) {
      console.log(error);
      audioEl.value && audioEl.value.pause();
    }
  }
};

const stopVoice = () => {
  audioEl.value && audioEl.value.pause();
  paramInfo.value.forEach((item) => {
    item.historyMessage.forEach((message) => {
      message.isPlay = false;
    });
  });
  currMessageId.value = null;
};

const getBase64 = async (messageId) => {
  try {
    const res = await reqBase64(messageId);
    const { data } = res || {};
    const base64Audio = `data:audio/mpeg;base64,${data}`;
    const audioBlob = dataURItoBlob(base64Audio);
    return audioBlob;
  } catch (error) {
    console.log(error);
    message.error('播放失败');
  }
};

const getHandColor = (val) => {
  let firstHandCol;
  let secondHandCol;
  if (val === 1) {
    firstHandCol = '#00A0CC';
    secondHandCol = '';
  }
  if (val === 2) {
    secondHandCol = 'red';
    firstHandCol = '';
  }
  const style = {
    firstHand: { color: firstHandCol },
    secondHand: { color: secondHandCol },
  };
  return style;
};

const isSupportClear = () => {
  const isflag = currentGroupId.value && !hasNormalOrThinkingStatus(paramInfo.value);
  return isflag;
};

const isSupportSend = () => {
  const isflag = serviceList.value.length && textVal.value.trim() && !hasNormalOrThinkingStatus(paramInfo.value);
  return isflag;
};

const isHasThinking = ref(false);
// const isAllChatEnd = ref(true);
watch(
  () => paramInfo.value,
  () => {
    const result = paramInfo.value.filter((item) => (item.historyMessage.some((h) => h.status == 'normal') ? item : false));
    isHasThinking.value = result.length ? true : false;
  },
  { immediate: false, deep: true }
);

const handleFocus = () => {
  isShowQuestionBoxBoder.value = true;
};

const handleBlur = () => {
  isShowQuestionBoxBoder.value = false;
};
//抽屉
const showDrawer = () => {
  openDrawer.value = !openDrawer.value;
};

const drawerClosed = (data) => {
  openDrawer.value = false;
  if (data) {
    textVal.value = data;
  }
};

watch(
  () => route.params.groupId,
  async (newId, oldId) => {
    if (newId !== oldId) {
      currentGroupId.value = newId;
      scrollToBottom();
    }
    //暂停语音播报
    currMessageId.value = null;
    audioEl.value && audioEl.value.pause();
  },
  { immediate: false, deep: true }
);

onMounted(() => {
  currentGroupId.value = route.params.groupId;
  textareaRef.value = document.querySelector('.question-textarea .ant-input');
});

onUnmounted(() => {
  audioEl.value && audioEl.value.pause();
  if (currentGroupId.value) {
    goStop();
  }
});
</script>

<style lang="less" scoped>
.model-dialog {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .model-dialog-main {
    width: 100%;
    height: calc(100% - 196px);
    padding: 12px 24px;
    .talk-content {
      display: flex;
      width: 100%;
      overflow-x: auto;
      height: 100%;
      padding-bottom: 16px;
      .one-chat {
        flex: 1;
        height: 100%;
        overflow-y: auto;
        min-width: 387px;
        padding: 8px 16px;
        overflow-x: hidden;
        h4 {
          font-size: 16px;
          font-weight: 600;
          padding-left: 40px;
        }
      }
      .one-chat:not(:first-child) {
        border-left: 1px solid rgba(0, 4, 26, 0.08);
      }

      .init-welcome-message {
        margin: 24px 0 24px 0;
        display: flex;
        .icon-img {
          width: 32px;
          height: 32px;
          margin-right: 8px;
        }
        .introduce {
          width: 100%;
          background: #fff;
          padding: 20px;
          h2 {
            color: #121f2c;
            font-size: 18px;
            font-weight: 600;
            line-height: 28px;
            margin-bottom: 8px;
          }

          p {
            color: #5e6974;
            font-size: 14px;
            line-height: 22px;
          }
        }
      }
      .dialog-case {
        flex: 1;
        width: 100%;
        display: flex;
        flex-direction: column;
        margin-left: 10px;
        margin-bottom: 24px;
        img {
          width: 32px;
          height: 32px;
          margin-right: 8px;
        }
        .case-user {
          display: flex;
          margin-bottom: 24px;
          font-size: 16px;
          img {
            margin-top: 20px;
          }
          .user-question {
            font-weight: 500;
            font-size: 16px;
            color: #00141a;
            width: 100%;
            padding: 20px 0;
            line-height: 22px;
            white-space: normal;
            word-break: break-word;
          }
        }
        .case-answer {
          display: flex;
          margin-bottom: 24px;
          font-size: 16px;
          .answer-box {
            width: 100%;
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            border: 1px solid rgba(18, 31, 44, 0.08);
            .front-icon {
              margin-right: 8px;
            }
            .box-txt {
              font-size: 16px;
              color: #00141a;
              line-height: 24px;
              font-weight: 400;
              white-space: normal;
              word-break: break-word;
              display: inline-block;
            }
            .stopped-tip {
              color: rgba(0, 20, 26, 0.25);
              font-size: 14px;
              padding-top: 8px;
            }
            .operate-btn {
              font-size: 14px;
              display: flex;
              align-items: center;
              .zan-icon,
              .cai-icon,
              .copy-icon,
              .line-icon,
              .voice-icon {
                margin-right: 20px;
                cursor: pointer;
              }
              .copy-icon:active {
                color: #00a0cc;
              }
              .line-icon {
                width: 1px;
                height: 16px;
                background: #00141a;
                opacity: 0.08;
              }
              .sound-btn {
                color: #00a0cc;
                display: flex;
                align-items: center;
                img {
                  width: 18px;
                  height: 18px;
                  cursor: pointer;
                }
              }
            }
            .box-bottom {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: 25px;
              .again-btn {
                color: #00a0cc;
                cursor: pointer;
                .again-icon {
                  margin-right: 8px;
                }
              }
            }
          }
          .custom-exception-tip {
            .word-btn {
              color: #00a0cc;
              cursor: pointer;
              padding: 0 4px;
            }
          }
        }
      }
    }
    .empty-box {
      // padding: 100px 0 0 0;
      // text-align: center;
      display: flex;
      justify-content: center; /* 水平居中 */
      align-items: center; /* 垂直居中 */
      height: 100%;
      img {
        width: 80px;
        height: 80px;
        margin-left: 7px;
      }
      p {
        margin-top: 8px;
        font-size: 16px;
      }
    }
  }

  .question-box {
    padding: 0 24px 16px 24px;
    .question-box-top {
      padding: 8px 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: rgba(0, 20, 26, 0.25);
      font-size: 14px;
      .clear-btn {
        font-size: 14px;
        color: #00141a;
        line-height: 22px;
        .clear-icon {
          margin-right: 4px;
        }
        &:hover {
          color: #00adcc;
        }
      }
      .clear-disabled-btn {
        color: rgba(0, 20, 26, 0.25);
      }
      .stop-btn {
        border: 1px solid rgba(0, 20, 26, 0.15);
        border-radius: 21px;
        color: rgba(0, 20, 26, 0.7);
        padding: 5px 16px;
        cursor: pointer;
        .stop-icon {
          font-size: 16px;
          padding-right: 4px;
        }
        &:hover {
          color: #2bb4d6;
          border: 1px solid #2bb4d6;
        }
      }
      .tip-word {
        color: #00141a;
        .tip-word-icon {
          margin-right: 8px;
        }
        &:hover {
          cursor: pointer;
          color: #00a0cc;
        }
      }

      .upload-btn {
        color: #00141a;
        padding-left: 8px;
        display: inline-block;
      }
      .ant-btn {
        padding: 0;
      }
    }

    .question-box-center {
      min-height: 112px;
      position: relative;
      padding: 12px 16px 16px 5px;
      background: #fff;
      border: 1px solid rgba(0, 20, 26, 0.15);
      .upload-content {
        width: 48px;
        height: 48px;
        text-align: center;
        line-height: 48px;
        background: #f1f2fa;
        position: relative;
        .loading-icon {
          font-size: 14px;
        }
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
        .delete-icon {
          font-size: 12px;
          position: absolute;
          top: -4px;
          right: -4px;
        }
      }
      .question-textarea {
        resize: none;
        border: none;
        margin-bottom: 32px;
      }
      :deep(.ant-input-textarea-show-count > .ant-input) {
        border: none;
      }
      :deep(.ant-input-textarea-show-count::after) {
        position: relative;
        top: 29px;
        right: 55px;
      }
      .send-btn {
        position: absolute;
        bottom: 16px;
        right: 16px;
        width: 48px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        justify-self: right;
        img {
          width: 48px;
          height: 32px;
        }
      }
    }
    .box-focus {
      border-color: #00a0cc;
    }

    .question-box-tip {
      font-size: 12px;
      color: rgba(0, 20, 26, 0.4);
      margin-top: 8px;
    }
  }

  :deep(.ant-input:focus) {
    border-color: transparent;
    box-shadow: none;
  }

  :deep(.ant-input-focused) {
    border-color: transparent;
    box-shadow: none;
  }

  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent;
  }

  ::-webkit-scrollbar-thumb {
    width: 4px;
    height: 94px;
    background: rgba(0, 20, 26, 0.15);
    border-radius: 4px;
  }
}
</style>
