<template>
  <div class="service-online-test-container">
    <!-- 请求信息 -->
    <div class="service-online-test-content">
      <div class="service-online-test-content-title">参数输入</div>
      <div class="service-online-test-content-box">
        <a-row v-if="streamTypeShow" class="line">
          <a-col class="label"><div>返回方式</div></a-col>
          <a-col class="item" :span="22">
            <a-select v-model:value="streamType" :options="STREAM_TYPE_OPTION" style="width: 160px" placeholder="请选择" @change="handleStreamTypeChange"></a-select>
          </a-col>
        </a-row>
        <a-row class="line">
          <a-col class="label"><div>请求地址</div></a-col>
          <a-col class="item" :span="22">
            <a-space-compact block>
              <a-select v-model:value="serviceName" style="width: 160px" placeholder="请选择" @change="handleServiceNameChange">
                <a-select-option v-for="(item, index) in SERVICE_NAME_OPTIONS" :key="index" :value="item.value">
                  <a-tooltip>
                    <template #title>{{ item.value ? `${item.value}` : `${item.label}` }}</template>
                    <template v-if="item.value.length > 15">{{ `${item.value.slice(0, 15)}...${item.value.slice(item.value?.length - 6, item.value?.length)}` }}</template>
                    <template v-else>{{ `${item.value}` }}</template>
                  </a-tooltip>
                </a-select-option>
              </a-select>
              <a-select v-model:value="requestType" :options="REQUEST_TYPE_OPTION" style="width: 120px"></a-select>
              <a-select v-model:value="sendUrl" style="width: 50%" placeholder="请选择" @change="handleSendUrlChange">
                <a-select-option v-for="(item, index) in SEND_URL_OPTIONS" :key="index" :value="item.value">
                  <a-tooltip>
                    <template #title>{{ item.value ? `${item.value}` : `${item.label}` }}</template>
                    <template v-if="item.value.length > 15">{{ `${item.value.slice(0, 80)}...${item.value.slice(item.value?.length - 6, item.value?.length)}` }}</template>
                    <template v-else>{{ `${item.value}` }}</template>
                  </a-tooltip>
                </a-select-option>
              </a-select>
              <a-input v-model:value="customText" style="width: 220px" :maxlength="100"></a-input>
            </a-space-compact>
          </a-col>
        </a-row>
        <a-row class="line">
          <a-col class="label"><div>参数类型</div></a-col>
          <a-col class="item" :span="22">
            <a-radio-group v-model:value="activeKey">
              <a-radio value="Body">Body</a-radio>
              <a-radio value="Params">Params</a-radio>
              <a-radio value="Headers">Headers</a-radio>
            </a-radio-group>
          </a-col>
        </a-row>
        <a-row class="line" style="display: flex; align-items: flex-start">
          <a-col class="label"><div>参数配置</div></a-col>
          <a-col class="item" :span="22">
            <div v-show="activeKey === 'Body'">
              <a-radio-group v-model:value="bodyType" @change="handleBodyTypeChange">
                <a-radio value="raw">raw</a-radio>
                <a-radio value="binary">binary</a-radio>
              </a-radio-group>
              <div v-show="bodyType === 'raw'" class="raw-codemirror-box">
                <div id="bodyTypeParentNode"></div>
                <!-- <a-textarea v-model:value="rawContent" :maxlength="rawContentMaxNum"></a-textarea> -->
              </div>
              <div v-show="bodyType === 'binary'" class="binary-box">
                <a-upload :file-list="fileList" :before-upload="beforeUpload" :disabled="uploadBtnDisebled" :max-count="1" :accept="acceptTypes" @remove="handleRemoveFile">
                  <a-button class="upload-btn"><jt-icon type="iconbianji" /><span>上传文件</span></a-button>
                  <template #itemRender="{ file, actions }">
                    <a-space style="margin-top: 8px">
                      <span class="fileName">{{ file.name }}</span>
                      <span class="fileSize">{{ toGB(file.size).size }}{{ toGB(file.size).unit }}</span>
                      <jt-icon type="iconshanchu1" @click="actions.remove"></jt-icon>
                    </a-space>
                  </template>
                </a-upload>
                <div class="upload-tip">支持扩展名：.png.psd.jpg.jpeg.bmp.gif.webp.svg.tiff</div>
              </div>
            </div>
            <div v-show="activeKey === 'Params'">
              <paramsTable ref="paramsTableRef"></paramsTable>
            </div>
            <div v-show="activeKey === 'Headers'">
              <HeadersTable ref="headersTableRef"></HeadersTable>
            </div>
          </a-col>
        </a-row>
      </div>
      <a-row class="line">
        <a-col class="label" style="width: 96px"></a-col>
        <a-col class="item" :span="22">
          <div class="btn-box"><a-button @click="handleSend">发送请求</a-button></div>
        </a-col>
      </a-row>
    </div>
    <a-divider style="margin: 0 0 32px 0" />
    <!-- 响应信息 -->
    <div class="service-online-test-content">
      <a-space :size="12">
        <div class="service-online-test-content-title">响应信息</div>
        <a-space :size="8" style="margin-bottom: 4px">
          <jt-icon type="iconwarning-circle-fill" class="iconwarning-color" />
          <span class="warning-text">
            <span>免责声明 当前模块仅作调试使用，生成内容仅供参考。请您仔细阅读已签署的<a href="javascript:;" class="link-text" @click="goToStatementPage">《 焕新社区服务声明 》</a>，遵守国家法律法规，合法合规使用生成式人工智能服务。</span>
          </span>
        </a-space>
      </a-space>

      <div class="service-online-test-content-box" style="padding: 0; margin-top: 16px">
        <div v-show="response !== undefined && response !== null && responseFormatObj.statusText" class="status-bar-box">
          <span class="tag-box"
            ><a-tag :color="!responseFormatObj.status ? 'red' : responseFormatObj.status == 200 ? 'green' : 'orange'">{{ responseFormatObj.status }}</a-tag></span
          >
          <span class="tag-box"
            ><a-tag color="default">{{ responseFormatObj.statusText }}</a-tag></span
          >
          <span v-if="responseFormatObj['content-Length']" class="tag-box"
            ><a-tag color="default">{{ responseFormatObj['content-Length'] }}</a-tag></span
          >
          <span v-if="responseFormatObj.date" class="tag-box"
            ><a-tag color="default">{{ responseFormatObj.date }}</a-tag></span
          >
        </div>
        <div v-show="response !== undefined && response !== null" class="response-box" :class="{ 'zero-height': response === undefined || response === null }">
          <div id="responseParentNode"></div>
        </div>
        <div v-show="response === null || response === undefined" class="response-box center-part empty-content">
          <serviceEmpty>
            <template #title>
              <div class="default-title-box">暂无响应信息</div>
            </template>
          </serviceEmpty>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { formatFwfwfsData, hexToDec } from '@/utils/service';
import { REQUEST_TYPE_OPTION, REQUEST_TYPE_ENUM, REQUEST_CONTENT_TYPE_MAP, STREAM_TYPE_ENUM, IMAGE_SELECT_ENUM, DEFAULT_STREAM_TYPE_OPTION } from '@/constants/service';
import { basicSetup } from 'codemirror';
import { EditorView, keymap } from '@codemirror/view';
import { EditorState } from '@codemirror/state';
// import { StreamLanguage } from '@codemirror/language';
import { indentWithTab } from '@codemirror/commands';
import { json } from '@codemirror/lang-json';
import paramsTable from './params-table.vue';
import HeadersTable from './headers-table.vue';
import { SEND } from '@/request/requestServiceTest';
import { fetchEventSource, EventStreamContentType } from '@microsoft/fetch-event-source';
import { keycloak } from '@/keycloak';
import isObject from 'lodash/isObject';
import { useStore } from 'vuex';
import { message, Upload } from 'ant-design-vue';
import { toGB, openInNewTab, getRedirectUrlPrefix } from '@/utils/index';
import serviceEmpty from '../service-empty.vue';
import dayjs from 'dayjs';
import { getPresetBodyContentByAddress } from '@/apis/service';
import isEqual from 'lodash/isEqual';
import { serviceStatementUrl } from '@/constants/index';

const Theme = EditorView.theme({
  '.cm-content': {
    fontSize: '14px',
  },
});
const extensions = [basicSetup, json(), keymap.of([indentWithTab]), EditorView.editable.of(false), EditorView.lineWrapping, Theme];
const store = useStore();
const detailData = inject('serviceDetailData'); // 服务详情页信息
const SERVICE_NAME_OPTIONS = ref([]); // 服务名称下拉选项
const serviceNameAndUrlMap = ref({}); // 服务名称与url的map结构
const codeeditor = ref(); // body下面的raw需要codemirror
const codeeditorRes = ref(null); // 响应请求的response的codemirror
const codeeditorResState = ref(null); // 返回的state对象
const paramsTableRef = ref(); // 参数列表的ref，用来获取参数数据的
const headersTableRef = ref(); // headers参数的ref
const bodyType = ref('raw'); // body下面的raw还是binary
const acceptTypes = '.jpg,.jpeg,.png,.bmp,.gif,.webp,.psd,.svg,.tiff,.JPG,.JPEG,.PNG,.BMP,.GIF,.WEBP,.PSD,.SVG,.TIFF';
const activeKey = ref('Body');
const SEND_URL_OPTIONS = ref([]); // url下拉选项
// const imageName = ref(''); // 平台自定义镜像或者框架的名称，用来请求请求体
const imageAddress = ref(''); // 平台自定义镜像或者框架的名称，用来请求请求体
const streamTypeShow = ref(false); // 返回方式是否展示
const streamType = ref(STREAM_TYPE_ENUM.NOT_STREAM); // 返回方式，流式或者非流式
// const STREAM_TYPE_OPTION = ref([{ value: STREAM_TYPE_ENUM.NOT_STREAM, label: '非流式返回' }]); // 默认是非流式下拉选项
const STREAM_TYPE_OPTION = ref(DEFAULT_STREAM_TYPE_OPTION);
const requestType = ref(REQUEST_TYPE_ENUM.POST); // 请求方式
const sendUrl = ref(''); // 请求的地址url
const serviceName = ref(''); // 选择的服务名称
const defaultHeaders = {
  // headers如果没有切换到那个tab
  Authorization: `Bearer ${keycloak.token}`,
}; // 默认的headers
const rawContent = ref(''); // raw的值
// const rawContentMaxNum = 55000; // raw的字数的最大值
// const isRawError = ref(false);
const fileList = ref([]); // 上传的文件列表
const customText = ref(''); // 用户填写的自定义内容
const response = ref(null); // 返回结果
const responseFormatObj = ref({}); // 返回结果要展示的信息
const fetchEventSourceRef = ref(); // 流式请求对象的ref
const presetBodyContent = ref([]); // 平台预置请求体的内容
const isRequestSuccess = ref(false); // 请求预制体成功后，记录为true，这样就不要再继续请求了，主要是为了避免detail接口循环请求然后这个接口也循环请求
// codemirror配置
const extensionsRaw = [
  basicSetup,
  keymap.of([indentWithTab]),
  json(),
  EditorView.updateListener.of(function (state) {
    if (state.docChanged) {
      rawContent.value = state.state.doc.toString();
    }
  }),
  EditorView.editable.of(true),
  Theme,
];
// 跳转到服务声明页面
const goToStatementPage = () => {
  openInNewTab(`${getRedirectUrlPrefix()}${serviceStatementUrl}`);
};
// 上传按钮是否禁用
const uploadBtnDisebled = computed(() => {
  return fileList.value?.length >= 1;
});
// url切换的变化的时候
const handleSendUrlChange = (value) => {
  sendUrl.value = value;
  const rightObj = presetBodyContent.value?.find((item) => value?.endsWith(item?.urlPrefix)) || {}; // 找到预置信息里面匹配后缀对应的对象，可能存在预置中，也可能不存在预置中
  streamType.value = rightObj?.type || STREAM_TYPE_ENUM.NOT_STREAM; // 对于没有找到流式方式还是非流式的方式，默认是非流式
  rawContent.value = rightObj?.type === streamType.value ? rightObj?.body || '' : ''; // 如果与当前的返回方式匹配，就需要填写body
  if (sendUrl.value?.endsWith('generate_stream')) {
    streamType.value = rightObj?.type || STREAM_TYPE_ENUM.STREAM;
  }
  codeeditor.value &&
    codeeditor.value?.setState(
      EditorState.create({
        doc: rawContent.value || '',
        extensions: extensionsRaw,
      })
    );
};
const streamStrTemp = ref(''); // 流式请求的string
// 返回方式切换的时候
const handleStreamTypeChange = (value) => {
  streamType.value = value;
  const rightObj = presetBodyContent.value?.find((item) => item?.type === value) || {}; // 找到对应type的对象
  const newSendUrl = SEND_URL_OPTIONS.value?.find((item) => item?.value?.endsWith(rightObj?.urlPrefix))?.value || undefined;
  if (newSendUrl) {
    sendUrl.value = newSendUrl;
  } else {
    if (streamType.value === STREAM_TYPE_ENUM.NOT_STREAM) {
      // 如果选中的是非流式的话，如果没有匹配上就默认选中第一个
      // sendUrl.value = SEND_URL_OPTIONS?.value?.[0]?.value;
      const newUrlNotStream = SEND_URL_OPTIONS?.value?.find((item) => !item?.value?.endsWith('generate_stream'))?.value || undefined;
      sendUrl.value = newUrlNotStream;
    } else {
      sendUrl.value = newSendUrl;
    }
  }
  rawContent.value = newSendUrl ? rightObj?.body || '' : ''; // 如果匹配上了配置预置体
  codeeditor.value &&
    codeeditor.value?.setState(
      EditorState.create({
        doc: rawContent.value || '',
        extensions: extensionsRaw,
      })
    );
};
// 确定字段与格式整理预置请求体
const formatPresetBody = (imageObj) => {
  const imagePresetExtensionObj = imageObj?.imagePresetExtension || {};
  const arr = [];
  if (imagePresetExtensionObj.port !== -1) {
    // 框架类型tgi,流式与非流式使用的是相同的请求体
    if (imagePresetExtensionObj.frameworkType === 0) {
      if (imagePresetExtensionObj?.streamResponse !== '-') {
        arr.push({
          type: STREAM_TYPE_ENUM.STREAM,
          urlPrefix: imagePresetExtensionObj.streamResponse,
          body: imagePresetExtensionObj?.streamRequest !== '-' ? imagePresetExtensionObj?.streamRequest : '',
        });
      }
      if (imagePresetExtensionObj?.batchResponse !== '-') {
        arr.push({
          type: STREAM_TYPE_ENUM.NOT_STREAM,
          urlPrefix: imagePresetExtensionObj.batchResponse,
          body: imagePresetExtensionObj?.streamRequest !== '-' ? imagePresetExtensionObj?.streamRequest : '',
        });
      }
    } else if (imagePresetExtensionObj.frameworkType === 1 || imagePresetExtensionObj.frameworkType === 2) {
      // 框架类型VLLm与openai，流式与非流式是相同的请求路径，但是2个请求体
      if (imagePresetExtensionObj?.streamResponse !== '-') {
        arr.push({
          type: STREAM_TYPE_ENUM.STREAM,
          urlPrefix: imagePresetExtensionObj.streamResponse,
          body: imagePresetExtensionObj?.streamRequest !== '-' ? imagePresetExtensionObj?.streamRequest : '',
        });
      }
      if (imagePresetExtensionObj?.streamResponse !== '-') {
        arr.push({
          type: STREAM_TYPE_ENUM.NOT_STREAM,
          urlPrefix: imagePresetExtensionObj.streamResponse,
          body: imagePresetExtensionObj?.batchRequest !== '-' ? imagePresetExtensionObj?.batchRequest : '',
        });
      }
    } else {
      if (imagePresetExtensionObj?.streamResponse !== '-') {
        arr.push({
          type: STREAM_TYPE_ENUM.STREAM,
          urlPrefix: imagePresetExtensionObj.streamResponse,
          body: imagePresetExtensionObj?.streamRequest !== '-' ? imagePresetExtensionObj?.streamRequest : '',
        });
      }
      if (imagePresetExtensionObj?.streamResponse !== '-') {
        arr.push({
          type: STREAM_TYPE_ENUM.NOT_STREAM,
          urlPrefix: imagePresetExtensionObj.streamResponse,
          body: imagePresetExtensionObj?.batchRequest !== '-' ? imagePresetExtensionObj?.batchRequest : '',
        });
      }
    }
  }
  return arr;
};
// 获取预置请求体
const getPresetBody = async () => {
  if (detailData.value?.instanceDetail?.imageType !== IMAGE_SELECT_ENUM.PLATFORM_PRESET_IMAGE || isRequestSuccess.value) {
    return;
  }
  try {
    const params = {
      address: imageAddress.value,
    };
    const res = await getPresetBodyContentByAddress(params);
    if (res?.code === 0) {
      isRequestSuccess.value = true; // 是否成功请求
      presetBodyContent.value = formatPresetBody(res?.data?.[0]) || [];
      // 是否展示返回方式
      streamTypeShow.value = detailData.value?.instanceDetail?.imageType === IMAGE_SELECT_ENUM.PLATFORM_PRESET_IMAGE && presetBodyContent.value?.length;
      // 如果数据不为空，需要生成返回方式的下拉选项
      if (presetBodyContent.value?.length) {
        handleStreamTypeChange(STREAM_TYPE_OPTION.value?.[0]?.value); // 默认选中第一个返回方式
      }
    } else {
      isRequestSuccess.value = false;
      presetBodyContent.value = [];
      rawContent.value = '';
    }
  } catch (e) {
    presetBodyContent.value = [];
    throw new Error(e);
  }
};
const extractObjectValues = (dataArray) => {
  return dataArray.reduce((acc, obj) => {
    return acc.concat(Object.values(obj));
  }, []);
};
// 整理服务名称下面的下拉选项并且生成对应的map
const formatServiceNameOptions = (data) => {
  const dataTemp = data?.value;
  const fwfwfsData = dataTemp?.services || []; // 服务访问方式数据,主要用服务访问方式的数据
  const temp = toRaw(fwfwfsData);
  // const services = formatFwfwfsData(temp) || [];
  const services = temp;
  // 生成服务名称下拉选项
  SERVICE_NAME_OPTIONS.value = toRaw(temp)?.map((item) => {
    return {
      value: item.name,
      label: item.name,
    };
  });
  // 生成服务名称对应请求地址的map结构
  const obj = {};
  // 三级域名修改
  services.forEach((item) => {
    obj[item.name] = extractObjectValues(item?.externalUrls) || [];
  });
  serviceNameAndUrlMap.value = obj; // 整理出来的serviceName与[url]的Object结构
  serviceName.value = SERVICE_NAME_OPTIONS.value?.[0]?.value; // 默认选择第一个服务名称
  SEND_URL_OPTIONS.value = toRaw(serviceNameAndUrlMap.value)[serviceName.value]?.map((item) => ({ value: item, label: item })); // 生成地址下拉选项
  handleSendUrlChange(SEND_URL_OPTIONS.value?.[0]?.value); // 默认选择第一个地址
  imageAddress.value = dataTemp?.instanceDetail?.image; // 部署的平台预置框架或者镜像地址
  getPresetBody();
};
// 初始化codemirror
const initCodeMirror = () => {
  if (toRaw(bodyType.value) === 'raw' && toRaw(activeKey.value) === 'Body' && !codeeditor.value) {
    nextTick(() => {
      codeeditor.value = new EditorView({
        extensions: extensionsRaw,
        parent: document.getElementById('bodyTypeParentNode'),
      });
    });
  }
};
// 初始化响应的codemirror
const initCodeMirrorRes = () => {
  if (!codeeditorRes.value) {
    nextTick(() => {
      codeeditorResState.value = EditorState.create({
        doc: '',
        extensions: extensions,
      });
      codeeditorRes.value = new EditorView({
        state: codeeditorResState.value,
        parent: document.getElementById('responseParentNode'),
      });
    });
  }
};
// body和raw变化的时候
const handleBodyTypeChange = (e) => {
  bodyType.value = e.target.value;
  initCodeMirror();
  // 清空数据
  rawContent.value = '';
  fileList.value = [];
};
// 服务名称变化的时候
const handleServiceNameChange = (value) => {
  serviceName.value = value;
  customText.value = '';
  SEND_URL_OPTIONS.value = toRaw(serviceNameAndUrlMap.value)?.[value]?.map((item) => ({ value: item, label: item })) || [];
  handleSendUrlChange(SEND_URL_OPTIONS.value?.[0]?.value);
};
// 处理在线调试的正常返回结果数据处理-axios
const formatResText = (res) => {
  let resultFormatObj = {};
  let insertStr = '';
  console.log('formatResText------------------------res', res);
  // 正常的模型
  resultFormatObj = res?.data;
  responseFormatObj.value = {
    status: res.status,
    statusText: res.statusText,
    'content-Length': res?.headers?.['content-length'],
    date: dayjs().format('YYYY.MM.DD HH:mm:ss'),
  };
  if (isObject(resultFormatObj)) {
    insertStr = JSON.stringify(resultFormatObj, null, '  ');
  } else {
    insertStr = resultFormatObj || '';
  }
  return insertStr;
};

const formatResTextStream = (res) => {
  const reader = res.body.getReader();
  const decoder = new TextDecoder('utf-8');
  const push = ({ value, done }) => {
    if (done) return streamStrTemp.value;
    const chunk = decoder.decode(value, { stream: true });
    streamStrTemp.value += hexToDec(chunk) + '\n';
    return reader.read().then(push);
  };
  return reader.read().then(push);
};
// 处理在线调试的的error返回结果-axios
const formatResErrorText = (error) => {
  let resultFormatObj = {};
  let insertStr = '';
  // 非200的情况是有response返回的，例如4xx，5xx
  console.log('error', JSON.stringify(error, null, '  '));
  console.log('error.response', error.response);
  if (error.response) {
    resultFormatObj = error.response?.data;
    responseFormatObj.value = {
      status: error.response.status,
      statusText: error.response.statusText,
      date: dayjs().format('YYYY.MM.DD HH:mm:ss'),
    };
    if (error?.response?.headers) {
      responseFormatObj.value['content-Length'] = error?.response?.headers?.['content-length'];
    }
  } else {
    // 像是跨域等网络错误是没有reponse的，就只有错误信息
    resultFormatObj = {
      data: error.message,
    };
    responseFormatObj.value = {};
  }
  if (isObject(resultFormatObj)) {
    insertStr = JSON.stringify(resultFormatObj, null, '  ');
  } else {
    insertStr = resultFormatObj || 'No body returned for response';
  }
  return insertStr;
};
// 删除上传文件
const handleRemoveFile = (file) => {
  const index = fileList.value.indexOf(file);
  const newFileList = fileList.value.slice();
  newFileList.splice(index, 1);
  fileList.value = newFileList;
};
// 检查上传文件类型
const checkFileType = (types, fileName) => {
  return types.split(',').includes('.' + fileName.split('.')[1]);
};
// 上传之前进行自动校验
const beforeUpload = (file) => {
  if (!checkFileType(acceptTypes, file.name)) {
    message.error('上传文件格式错误');
    return Upload.LIST_IGNORE;
  }
  const isLimit = file.size / 1024 / 1024 < 10;
  if (!isLimit) {
    message.error('大小不应该超过10M');
    return false;
  }
  fileList.value = [...(fileList.value || []), file];
  return false;
};
// 处理header头中配置的大小写
const mergeCaseInsensitiveFields = (jsonObj) => {
  // 创建一个新对象用于存储最终结果
  const result = {};
  // 遍历原始对象的键值对
  Object.keys(jsonObj).forEach((key) => {
    // 将键名转换为小写，用于判断是否重复
    const lowerKey = key.toLowerCase();
    result[lowerKey] = jsonObj[key];
  });
  return result;
};
// 发送数据
const handleSend = async () => {
  let params = {};
  let headers = {};
  let data = undefined;
  if (paramsTableRef?.value) {
    if (paramsTableRef?.value.getIsError()) {
      message.error('存在不合法字符');
      return;
    }
    params = paramsTableRef?.value?.getData();
  }
  if (headersTableRef?.value) {
    if (headersTableRef?.value.getIsError()) {
      message.error('存在不合法字符');
      return;
    }
    headers = mergeCaseInsensitiveFields(headersTableRef?.value?.getData());
  }
  console.log('request headers', headers);
  // 如果是raw的话，需要修改Content-Type
  if (bodyType.value === 'raw' && rawContent.value) {
    data = rawContent.value;
    // if (headers['Content-Type'] === undefined || headers['Content-Type'] === null) {
    //   headers['Content-Type'] = 'application/json';
    // }
  }
  if (bodyType.value === 'binary' && fileList.value?.[0]) {
    data = new Blob([fileList.value?.[0]], { type: REQUEST_CONTENT_TYPE_MAP.get(fileList.value?.[0].type) });
    // if (headers['Content-Type'] === undefined || headers['Content-Type'] === null) {
    //   headers['Content-Type'] = 'application/octet-stream';
    // }
  }
  if (!sendUrl.value) {
    message.error('url不能为空');
    return;
  }
  store.dispatch('updateGlobalLoading', true);
  streamStrTemp.value = '';
  response.value = '';
  responseFormatObj.value = {};
  codeeditorRes.value?.setState(
    EditorState.create({
      doc: response.value,
      extensions,
    })
  );
  // 流式请求,其余的全部默认走流式请求,url后面有generate_stream结尾的，或者data中有stream是true的
  if (streamType.value === STREAM_TYPE_ENUM.STREAM || sendUrl.value?.endsWith('generate_stream')) {
    let newUrl = `${sendUrl.value}${customText.value}`;
    if (Object.keys(params)?.length) {
      const queryParams = new URLSearchParams(params).toString();
      newUrl = `${url}?${queryParams}`;
    }
    fetchEventSourceRef.value = fetchEventSource(newUrl, {
      method: requestType.value,
      headers: {
        ...headers,
        // 'Content-Type': 'application/json',
        Accept: 'text/event-stream',
        Connection: 'keep-alive',
      },
      body: data,
      onopen: async (res) => {
        console.log('SSE-open', res);
        if (res.ok && res.headers.get('content-type') === EventStreamContentType) {
          responseFormatObj.value = {
            status: res?.status,
            statusText: res?.statusText,
            date: dayjs().format('YYYY.MM.DD HH:mm:ss'),
          };
          return; // 走onmessage逻辑
        } else if (res.ok && res.headers.get('content-type') !== EventStreamContentType) {
          try {
            await formatResTextStream(res);
            console.log('streamStrTemp.value res.ok !== EventStreamContentType------------', streamStrTemp.value);
            if (streamStrTemp.value) {
              responseFormatObj.value = {
                status: res?.status,
                statusText: res?.statusText,
                date: dayjs().format('YYYY.MM.DD HH:mm:ss'),
              };
              response.value = streamStrTemp.value;
              codeeditorRes.value?.setState(
                EditorState.create({
                  doc: response.value,
                  extensions,
                })
              );
            }
          } catch (e) {
            console.log('SSE-open reader---------------------- res.ok', e);
          }
          return;
        } else if (res?.status >= 400 && res?.status < 600) {
          try {
            await formatResTextStream(res);
            console.log('streamStrTemp.value res 400-600', streamStrTemp.value);
            if (streamStrTemp.value) {
              responseFormatObj.value = {
                status: res?.status,
                statusText: res?.statusText,
                date: dayjs().format('YYYY.MM.DD HH:mm:ss'),
              };
              console.log('responseFormatObj.value', responseFormatObj.value);
              response.value = streamStrTemp.value;
              codeeditorRes.value?.setState(
                EditorState.create({
                  doc: response.value,
                  extensions,
                })
              );
            }
          } catch (e) {
            console.log('SSE-open reader---------------------- 400-600', e);
          }
        } else {
          responseFormatObj.value = {
            status: res?.status,
            statusText: res?.statusText,
            date: dayjs().format('YYYY.MM.DD HH:mm:ss'),
          };
          response.value = '';
          store.dispatch('updateGlobalLoading', false);
          throw new Error();
        }
      },
      onmessage: (res) => {
        console.log('SSE-message', res);
        const { data } = res || {};
        if (data === '[DONE]') {
          console.log('结束');
          return;
        }
        const jsonData = JSON.parse(data);
        // 如果等于stop表示结束
        if (jsonData?.choices?.[0]?.finish_reason === 'stop') {
          console.log('结束 stop');
          return;
        }
        if (data) {
          streamStrTemp.value += data + '\n';
          console.log('🚀 --------- handleSend --------- str:', streamStrTemp.value);
          response.value = streamStrTemp.value;
          codeeditorRes.value?.setState(
            EditorState.create({
              doc: response.value,
              extensions,
            })
          );
        }
      },
      onerror: (error) => {
        console.log('SSE-onerror', error);
        if (!response.value) {
          response.value = '';
        }
        if (streamStrTemp.value) {
          response.value = streamStrTemp.value;
          codeeditorRes.value?.setState(
            EditorState.create({
              doc: response.value,
              extensions,
            })
          );
        }
        store.dispatch('updateGlobalLoading', false);
        throw error;
      },
      onclose: () => {
        console.log('SSE-onclose');
        if (!response.value) {
          response.value = '';
        }
        store.dispatch('updateGlobalLoading', false);
      },
    });
  } else {
    try {
      const res = await SEND(`${sendUrl.value}${customText.value}`, requestType.value, params, data, headers);
      response.value = formatResText(res);
      codeeditorRes.value?.setState(
        EditorState.create({
          doc: response.value,
          extensions,
        })
      );
      store.dispatch('updateGlobalLoading', false);
    } catch (e) {
      response.value = formatResErrorText(e);
      codeeditorRes.value?.setState(
        EditorState.create({
          doc: response.value,
          extensions,
        })
      );
      store.dispatch('updateGlobalLoading', false);
    }
  }
};
onBeforeUnmount(() => {
  // codemirror，清空parent节点下面的dom，解决codemirror的bug
  if (document.getElementById('responseParentNode')) {
    document.getElementById('responseParentNode').innerHTML = '';
  }
});
watch(
  () => detailData,
  (newVal, oldVal) => {
    if (!isEqual(newVal, oldVal)) {
      // 新值与旧值不一致时候才重新渲染
      formatServiceNameOptions(newVal);
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
watch(
  () => store.state.token,
  (val) => {
    defaultHeaders['Authorization'] = `Bearer ${val}`;
  },
  {
    deep: true,
    immediate: true,
  }
);
// 初始化2个codemirror
initCodeMirror();
initCodeMirrorRes();
</script>
<style lang="less" scoped>
.service-online-test-container {
  box-sizing: border-box;
  padding: 24px 20px;
  background-color: #fff;
  margin: 20px;
  .service-online-test-content {
    &-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
      color: #00141a;
    }
    &-box {
      padding: 20px 26px 0 26px;
      .line {
        align-items: center;
        margin-bottom: 24px;
        .label {
          margin-right: 16px;
          color: #00141a;
        }
        .raw-codemirror-box,
        .binary-box {
          margin-top: 16px;
          position: relative;
        }
        .raw-codemirror-box {
          :deep(.cm-editor) {
            outline: 1px solid rgba(0, 20, 26, 0.15);
          }
          :deep(.cm-gutters) {
            display: none;
          }
        }
        .upload-btn {
          width: 110px;
          height: 32px;
          color: rgba(0, 20, 26, 0.7);
        }
        .upload-tip {
          color: rgba(0, 20, 26, 0.45);
          margin-top: 8px;
        }
        .fileName {
          color: rgba(0, 20, 26, 0.7);
          margin-right: 8px;
        }
        .fileSize {
          color: rgba(0, 20, 26, 0.45);
          margin-right: 16px;
          transform: translateY(2px);
          display: inline-block;
        }
      }
      .error-text {
        font-weight: 400;
        font-size: 12px;
        color: #ff454d;
        margin-top: 4px;
      }
      .error-box {
        border: 1px solid #f53922;
      }
    }
    .btn-box {
      margin-top: 8px;
      margin-bottom: 32px;
      button {
        background: linear-gradient(90deg, #00cada 0%, #00a2f4 100%);
        border-radius: 2px;
        color: #fff;
        border: none;
      }
    }
    .response-box {
      min-height: 400px;
      .default-title-box {
        text-align: center;
      }
    }
    .status-bar-box {
      padding: 4px;
      border: 1px solid rgba(0, 20, 26, 0.08);
      .tag-box {
        display: inline-block;
        margin-right: 8px;
        :deep(.ant-tag) {
          font-size: 14px;
        }
      }
    }
    .center-part {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .zero-height {
      min-height: 0;
      height: 0;
    }
    .empty-content {
      background: rgba(0, 20, 26, 0.04);
    }
    .iconwarning-color {
      color: #00b2cc;
    }
    .warning-text {
      color: rgba(0, 20, 26, 0.7);
    }
    .link-text {
      color: #00a0cc;
    }
  }
}
</style>
